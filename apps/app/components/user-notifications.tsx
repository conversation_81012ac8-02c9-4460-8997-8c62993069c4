"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/sheet";
import { Button } from "@workspace/ui/components/button";
import { IconBell } from "@tabler/icons-react";
import { User } from "@/lib/types";
import {
  Tabs,
  TabsContent,
} from "@workspace/ui/components/tabs";
import { useState } from "react";
import { IconButton } from "@workspace/ui/components/icon-button";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Badge } from "@workspace/ui/components/badge";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { formatDistanceToNow } from 'date-fns';
import { UserAvatar } from "./user-avatar";
import Link from "next/link";

export const UserNotifications = ({ user }: { user: User }) => {
  const [selectedTab, setSelectedTab] = useState<"all" | "buyer" | "seller">("all");
  const [isOpen, setIsOpen] = useState(false);
  
  const notificationsData = useQuery(api.users.getNotifications, {
    paginationOpts: { numItems: 10, cursor: null },
  }); 
  const unreadCount = useQuery(api.users.getUnreadNotificationsCount) || 0;
  const markAsRead = useMutation(api.users.markNotificationsAsRead);
  const followUser = useMutation(api.users.followUser);
  const unfollowUser = useMutation(api.users.unfollowUser);

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open && unreadCount > 0) {
      markAsRead();
    }
  };

  const handleFollowToggle = async (userId: Id<"users">, isFollowing: boolean) => {
    try {
      if (isFollowing) {
        await unfollowUser({ targetUserId: userId });
      } else {
        await followUser({ targetUserId: userId });
      }
    } catch (error) {
      console.error("Error toggling follow:", error);
    }
  };

  const getGroupedNotifications = () => {
    const currentNotifications = notificationsData?.page;
    if (!currentNotifications) return { New: [], Earlier: [] };
    
    const now = new Date();
    const grouped: Record<string, any[]> = {
      New: [],
      Earlier: [],
    };

    currentNotifications.forEach(notification => {
      if (!notification) return;
      const notificationDate = new Date(notification._creationTime);
      const hoursDiff = (now.getTime() - notificationDate.getTime()) / (1000 * 60 * 60);
      
      if (hoursDiff < 24) {
        grouped.New?.push(notification);
      } else {
        grouped.Earlier?.push(notification);
      }
    });

    return grouped;
  };

  const groupedNotifications = getGroupedNotifications();

  const getFilteredNotifications = (tabType: "all" | "buyer" | "seller") => {
    if (tabType === "all") return groupedNotifications;
    
    const filtered: Record<string, any[]> = {
      New: [],
      Earlier: [],
    };
    
    Object.entries(groupedNotifications).forEach(([key, items]) => {
      filtered[key] = items.filter(notification => {
        if (!notification) return false;
        if (tabType === "buyer" && notification.type?.includes("buyer")) {
          return true;
        }
        if (tabType === "seller" && notification.type?.includes("seller")) {
          return true;
        }
        return false;
      });
    });
    
    return filtered;
  };

  const filteredNotifications = getFilteredNotifications(selectedTab);

  const renderNotification = (notification: any) => {
    const { type, actor, _creationTime } = notification;
    const timeAgo = formatDistanceToNow(new Date(_creationTime), { addSuffix: true });
    
    if (type === "follow") {
      return (
        <div key={notification._id} className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <UserAvatar user={actor} size="default" />
            <div>
              <p className="text-sm">
                <span className="font-semibold">
                  {actor?.username || "User"}
                </span>{" "}
                started following you
              </p>
              <p className="text-xs text-muted-foreground">
                {timeAgo}
              </p>
            </div>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => handleFollowToggle(actor._id, actor.isFollowing)}
          >
            {actor.isFollowing ? "Unfollow" : "Follow"}
          </Button>
        </div>
      );
    }

    if (type === "tag" || type === "mention") {
      return (
        <div key={notification._id} className="flex items-center gap-3">
          <UserAvatar user={actor} size="default" />
          <div>
            <div className="text-sm">
              <div className="flex flex-col gap-1">
                <div className="">
                  <span className="font-semibold">
                    @{actor?.username || "User"}
                  </span>{" "} tagged you in {" "}
                  {notification.message || (
                    <Link className="text-blue-500" href={`/stream/${notification.data?.streamId}`}>
                      {notification.data?.streamHostUsername}'s stream
                    </Link>
                  )} and said <span className="text-muted-foreground">"{notification.data?.originalMessage}"</span>
                </div>

              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              {timeAgo}
            </p>
          </div>
        </div>
      );
    }
    
    return (
      <div key={notification._id} className="flex items-center gap-3">
        <UserAvatar user={actor} size="default" />
        <div>
          <p className="text-sm">
            <span className="font-semibold">
              @{actor?.username || "User"}
            </span>{" "}
            {notification.message || "interacted with you"}
          </p>
          <p className="text-xs text-muted-foreground">
            {timeAgo}
          </p>
        </div>
      </div>
    );
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={handleOpenChange}>
        <SheetTrigger asChild>
          <div className="relative">
            <IconButton icon={IconBell} />
            {unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center"
              >
                {unreadCount > 9 ? '9+' : unreadCount}
              </Badge>
            )}
          </div>
        </SheetTrigger>
        <SheetContent
          close={false}
          overlay={false}
          className="md:!max-w-md sm:!max-w-lg"
        >
          <SheetHeader className="border-b border-input">
            <SheetTitle className="text-xl font-semibold">
              Notifications
            </SheetTitle>
            <SheetDescription></SheetDescription>
          </SheetHeader>

          <div className="p-4 flex-1">
            <Tabs
              value={selectedTab}
              onValueChange={(value) =>
                setSelectedTab(value as "all" | "buyer" | "seller")
              }
              className="w-full"
            >
              {/* <TabsList className="w-full justify-start gap-1 bg-transparent border-b rounded-none">
                <TabsTrigger
                  value="all"
                  className={cn(
                    "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                    "data-[state=active]:text-foreground",
                    "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                  )}
                >
                  All
                </TabsTrigger>
                <TabsTrigger
                  value="buyer"
                  className={cn(
                    "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                    "data-[state=active]:text-foreground",
                    "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                  )}
                >
                  Buyer
                </TabsTrigger>
                <TabsTrigger
                  value="seller"
                  className={cn(
                    "pb-3 -mb-px rounded-none h-auto px-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent",
                    "data-[state=active]:text-foreground",
                    "data-[state=inactive]:border-transparent data-[state=inactive]:text-muted-foreground",
                  )}
                >
                  Seller
                </TabsTrigger>
              </TabsList> */}

              <TabsContent value="all">
                {notificationsData === undefined ? (
                  <div className="text-sm text-muted-foreground">Loading notifications...</div>
                ) : notificationsData.page?.length === 0 ? (
                  <div className="text-sm text-muted-foreground">No notifications yet</div>
                ) : (
                  <div className="space-y-6">
                    {filteredNotifications.New && filteredNotifications.New.length > 0 && (
                      <div>
                        <h3 className="font-semibold mb-3">New</h3>
                        <div className="space-y-4">
                          {filteredNotifications.New.map(renderNotification)}
                        </div>
                      </div>
                    )}

                    {filteredNotifications.Earlier && filteredNotifications.Earlier.length > 0 && (
                      <div>
                        <h3 className="font-semibold mb-3">Earlier</h3>
                        <div className="space-y-4">
                          {filteredNotifications.Earlier.map(renderNotification)}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="buyer" className="mt-4">
                {notificationsData === undefined ? (
                  <div className="text-sm text-muted-foreground">Loading notifications...</div>
                ) : (!filteredNotifications.New?.length && !filteredNotifications.Earlier?.length) && notificationsData.page?.length === 0 ? (
                  <div className="text-sm text-muted-foreground">No buyer notifications</div>
                ) : (
                  <div className="space-y-6">
                    {filteredNotifications.New && filteredNotifications.New.length > 0 && (
                      <div>
                        <h3 className="font-semibold mb-3">New</h3>
                        <div className="space-y-4">
                          {filteredNotifications.New.map(renderNotification)}
                        </div>
                      </div>
                    )}

                    {filteredNotifications.Earlier && filteredNotifications.Earlier.length > 0 && (
                      <div>
                        <h3 className="font-semibold mb-3">Earlier</h3>
                        <div className="space-y-4">
                          {filteredNotifications.Earlier.map(renderNotification)}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="seller" className="mt-4">
                {notificationsData === undefined ? (
                  <div className="text-sm text-muted-foreground">Loading notifications...</div>
                ) : !filteredNotifications.New?.length && !filteredNotifications.Earlier?.length ? (
                  <div className="text-sm text-muted-foreground">No seller notifications</div>
                ) : (
                  <div className="space-y-6">
                    {filteredNotifications.New && filteredNotifications.New.length > 0 && (
                      <div>
                        <h3 className="font-semibold mb-3">New</h3>
                        <div className="space-y-4">
                          {filteredNotifications.New.map(renderNotification)}
                        </div>
                      </div>
                    )}

                    {filteredNotifications.Earlier && filteredNotifications.Earlier.length > 0 && (
                      <div>
                        <h3 className="font-semibold mb-3">Earlier</h3>
                        <div className="space-y-4">
                          {filteredNotifications.Earlier.map(renderNotification)}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
};
