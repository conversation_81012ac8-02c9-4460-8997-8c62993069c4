import {
  <PERSON>et,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/sheet";
import { Button } from "@workspace/ui/components/button";
import { IconLogout, IconChevronRight, IconScreenShare } from "@tabler/icons-react";
import { useAuthActions } from "@convex-dev/auth/react";
import { User } from "@/lib/types";
import { motion } from "framer-motion";
import Link from "next/link";
import { useState } from "react";
import { UserAvatar } from "./user-avatar";
import { Separator } from "@workspace/ui/components/separator";
import { useRouter } from "next/navigation";

export const UserInfo = ({ user }: { user: User }) => {
  const { signOut } = useAuthActions();
  const [open, setOpen] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    await signOut();
  };

  const handleProfileClick = () => {
    setOpen(false);
  };

  return (
    <>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger>
          <motion.div
            initial={{ background: "none" }}
            whileHover={{
              background:
                "linear-gradient(90deg, transparent 0%, rgba(192,192,192,0.3) 50%, transparent 100%)",
              backgroundSize: "200% 100%",
              transition: {
                backgroundPosition: {
                  duration: 1,
                  repeat: Infinity,
                  ease: "linear",
                },
              },
            }}
            animate={{
              backgroundPosition: ["100% 0%", "-100% 0%"],
            }}
            style={{
              borderRadius: "50%",
              position: "relative",
            }}
          >
            <UserAvatar
              user={user}
              size="default"
            />
          </motion.div>
        </SheetTrigger>
        <SheetContent close={false} overlay={false}>
          <SheetHeader className="border-b border-input">
            <SheetTitle className="sr-only">User Info</SheetTitle>
            <SheetDescription></SheetDescription>
            <Link href={`/user/${user?.username}`} onClick={handleProfileClick}>
              <div className="flex items-center gap-4 cursor-pointer hover:bg-accent p-2 rounded-md group relative">
                <UserAvatar user={user} size="default" />
                <div className="flex flex-col">
                  <span className="text-md font-semibold">
                    {user?.username}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {user?.following?.length} following | {user?.followers?.length} followers
                  </span>
                </div>
                <IconChevronRight
                  className="absolute right-2 opacity-0 group-hover:opacity-50 transition-opacity"
                  size={12}
                />
              </div>
            </Link>
          </SheetHeader>
          <div className="grid grid-cols-3 gap-3 px-3">
            <div 
              onClick={() => router.push("/settings/sellers/stream")}
              className="flex flex-col items-center justify-center p-3 rounded-lg hover:bg-accent cursor-pointer border border-input"
            >
              <span role="img" aria-label="tv" className="text-2xl mb-1">🎥</span>
              <span className="text-xs font-medium">Streams</span>
            </div>
            <div 
              onClick={() => router.push("/settings/buyers/payments")}
              className="flex flex-col items-center justify-center p-3 rounded-lg hover:bg-accent cursor-pointer border border-input"
            >
              <span role="img" aria-label="payments" className="text-2xl mb-1">💳</span>
              <span className="text-xs font-medium">Payments & Shipping</span>
            </div>
            <div className="flex flex-col items-center justify-center p-3 rounded-lg border border-input opacity-50 cursor-not-allowed">
              <span role="img" aria-label="saved" className="text-2xl mb-1">🔖</span>
              <span className="text-xs font-medium">Saved</span>
            </div>
            <div className="flex flex-col items-center justify-center p-3 rounded-lg border border-input opacity-50 cursor-not-allowed">
              <span role="img" aria-label="bids" className="text-2xl mb-1">🎯</span>
              <span className="text-xs font-medium">Bids & Offers</span>
            </div>
            <div className="flex flex-col items-center justify-center p-3 rounded-lg border border-input opacity-50 cursor-not-allowed">
              <span role="img" aria-label="purchases" className="text-2xl mb-1">🛍️</span>
              <span className="text-xs font-medium">Purchases</span>
            </div>
            <div className="flex flex-col items-center justify-center p-3 rounded-lg border border-input opacity-50 cursor-not-allowed">
              <span role="img" aria-label="health" className="text-2xl mb-1">🛡️</span>
              <span className="text-xs font-medium">Account Health</span>
            </div>
          </div>
          
          <Separator />
          <div className="px-4 pb-2">
            <Link href="/settings/account">
              <div className="flex items-center justify-between p-2 rounded-xl border border-transparent hover:bg-muted/50 hover:border hover:border-zinc-800 cursor-pointer">
                <span>Account Settings</span>
                <IconChevronRight size={16} className="opacity-50" />
              </div>
            </Link>
          </div>
          <div className="flex-1"></div>
          <SheetFooter className="border-t border-input p-4">
            <Button variant="outline" className="w-full" onClick={handleLogout}>
              <IconLogout className="mr-2" />
              Logout
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </>
  );
};
