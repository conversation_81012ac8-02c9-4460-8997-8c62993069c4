"use client";

import { AppSidebar } from "@/components/layout/app-sidebar";
import { UserInfo } from "@/components/user-info";
import { Search } from "@/components/search";
import { Separator } from "@workspace/ui/components/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@workspace/ui/components/sidebar";
import { IconSquareRoundedPlus } from "@tabler/icons-react";
import { Button } from "@workspace/ui/components/button";
import { usePreloadedData } from "@/hooks/use-preloaded-data";
import { usePreloadedQuery } from "convex/react";
import { UserActivity } from "@/components/user-activity";
import { UserNotifications } from "../user-notifications";
import { usePathname } from "next/navigation";
import { BecomeASellerDialog } from "@/components/become-a-seller";
import { User } from "@/lib/types";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@workspace/ui/components/dropdown-menu";
import Link from "next/link";
import { UpdateNotificationPopup } from "@/components/update-notification-popup";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { preloadedUser } = usePreloadedData();
  const user = usePreloadedQuery(preloadedUser);

  const pathname = usePathname();
  const isSettingsPage = pathname?.includes("/settings");

  return (
    <SidebarProvider>
      <UpdateNotificationPopup />
      <AppSidebar />
      <SidebarInset className="h-screen flex flex-col overflow-hidden [--header-padding:16px]">
        {!isSettingsPage && (
          <header className="sticky top-0 z-50 flex justify-between h-16 shrink-0 items-center border-b bg-sidebar w-full gap-2">
            <div className="flex items-center flex-1 min-w-0 pl-[var(--header-padding)]">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mx-3 h-4" />
              <div className="flex-1 min-w-0">
                <Search />
              </div>
            </div>
            <div className="flex items-center gap-3 shrink-0 pr-[var(--header-padding)]">
              {user?.role !== "seller" ? (
                <BecomeASellerDialog user={user as User} />
              ) : (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <IconSquareRoundedPlus className="!size-6" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href="/list-product">List a product</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/schedule">Schedule a stream</Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
              <UserActivity user={user as User} />
              <UserNotifications user={user as User} />
              <UserInfo user={user as User} />
            </div>
          </header>
        )}
        <div className="flex-1 overflow-auto">
          <div className="flex flex-col gap-4">{children}</div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
