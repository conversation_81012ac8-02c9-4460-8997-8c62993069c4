"use client";

import { useState } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import { Progress } from "@workspace/ui/components/progress";
import { useRouter } from "next/navigation";
import { SellerSubcategorySelection } from "./seller-subcategory-selection";
import { categories } from "@workspace/lib/constants/categories";
import Image from "next/image";

export function SellerCategorySelection() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showSubcategories, setShowSubcategories] = useState(false);

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setShowSubcategories(true);
  };

  const handleSubcategorySelect = (subcategoryId: string) => {
    router.push(
      `/become-a-seller/store-details?category=${selectedCategory}&subcategory=${subcategoryId}`,
    );
  };

  if (showSubcategories && selectedCategory) {
    return (
      <SellerSubcategorySelection
        category={selectedCategory}
        onBack={() => setShowSubcategories(false)}
        onSelect={handleSubcategorySelect}
      />
    );
  }

  return (
    <div className="flex flex-col h-full max-h-[85vh]">
      <div className="bg-gradient-to-br from-red-400/30 to-blue-500/20 px-8 py-8">
        <div className="flex-none mb-6">
          <div className="w-full h-2 bg-primary/10 rounded-full overflow-hidden">
            <div className="h-full w-[40%] bg-gradient-to-r from-red-400 to-blue-500 rounded-full"></div>
          </div>
          <div className="flex justify-between text-sm mt-3">
            <span className="text-zinc-500">Guidelines</span>
            <span className="font-medium">Categories</span>
            <span className="text-zinc-500">Experience</span>
            <span className="text-zinc-500">Additional Details</span>
          </div>
        </div>

        <div className="flex-none">
          <h1 className="text-3xl font-bold mb-2">
            In which category will you sell?
          </h1>
          <p className="text-zinc-600 dark:text-zinc-400">
            You can always request more categories later.
          </p>
        </div>
      </div>

      <div className="flex-1 min-h-0 overflow-auto px-8 py-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {categories.map((category) => (
            <div 
              key={category.id}
              className={`relative rounded-xl overflow-hidden border group cursor-pointer transition-all duration-300 transform hover:-translate-y-1`}
              onClick={() => handleCategorySelect(category.id)}
            >
              {/* Gradient border for selected cards */}
              {selectedCategory === category.id && (
                <div className="absolute -inset-[3px] rounded-[14px] bg-gradient-to-br from-red-400/70 to-blue-500/70 animate-pulse z-10"></div>
              )}
              
              <Card
                className={`relative overflow-hidden border-0 shadow-lg !p-0 ${
                  selectedCategory === category.id
                    ? "shadow-xl"
                    : "hover:shadow-xl"
                }`}
              >
                <div className="aspect-square relative overflow-hidden bg-gradient-to-br from-zinc-800 to-zinc-900">
                  {/* Background image */}
                  {category.image ? (
                    <Image
                      src={category.image}
                      alt={category.title}
                      className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      priority={categories.indexOf(category) < 8}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-4xl text-zinc-700 dark:text-zinc-500 opacity-30">{category.title[0]}</span>
                    </div>
                  )}
                  
                  {/* Darker overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-black/40"></div>
                  
                  {/* Content */}
                  <div className="absolute inset-0 flex flex-col justify-end p-4">
                    <h3 className="font-bold text-lg text-white mb-1">{category.title}</h3>
                    <p className="text-sm text-white/90 line-clamp-2">
                      {category.description}
                    </p>
                  </div>
                  
                  {/* Selection indicator */}
                  {selectedCategory === category.id && (
                    <div className="absolute top-3 right-3 bg-white dark:bg-zinc-800 rounded-full p-1 shadow-md z-20">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-primary">
                        <path fillRule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-between items-center px-8 py-6 border-t dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900/50">
        <Button variant="outline" size="lg" onClick={() => router.back()}>
          Back
        </Button>
        <Button
          size="lg"
          disabled={!selectedCategory}
          onClick={() => setShowSubcategories(true)}
          className="font-medium"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
