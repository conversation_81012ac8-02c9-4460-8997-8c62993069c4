import type { UserRole} from "@workspace/backend/convex/lib/types";

export interface Follow {
  _id: string;
  followerId: string;
  followingId: string;
  _creationTime: number;
}

export type UserImage = {
  type: "google" | "storage";
  url?: string;
  storageId?: string;
};

export type User = {
  _creationTime: number;
  _id: string;
  color: string;
  email: string;
  firstName: string;
  hashedPassword: boolean;
  isHost?: boolean;
  bio?: string;
  image?: UserImage;
  lastLoginType: string;
  lastName: string;
  name: string;
  role: UserRole;
  status: "online" | "offline";
  subscription: null | any;
  username: string;
  avatarUrl?: string;
  coverImage?: UserImage;
  coverImageUrl?: string;
  accounts: { provider: string }[];
  followers?: Follow[];
  following?: Follow[];
  isSellerInterested?: boolean;
  followersCount?: number;
  followingCount?: number;
};
