import { StreamVideoClient } from "@stream-io/video-react-native-sdk";

let client: StreamVideoClient | null = null;

export function getOrCreateStreamVideoClient(config: {
  apiKey: string;
  user: any;
  token: string;
}) {
  if (!client) {
    client = new StreamVideoClient(config);
  }
  return client;
}

export function disconnectStreamVideoClient() {
  if (client) {
    client.disconnectUser();
    client = null;
  }
}
