{"extends": "../../tsconfig.json", "compilerOptions": {"strict": true, "skipLibCheck": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "react-native", "types": ["node", "jest"], "allowJs": true, "resolveJsonModule": true, "esModuleInterop": true}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "app", "lib", "hooks", "constants", "../../packages/backend", "../../packages/lib"], "exclude": ["node_modules", "../../packages/backend/convex/_generated"]}