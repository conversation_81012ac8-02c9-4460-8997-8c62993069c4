import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { shipping_profiles } from "@workspace/lib/constants/shipping-profiles";
import CustomShippingProfile from "./custom-shipping-profile";

interface ShippingProfileModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (profile: string) => void;
}

export default function ShippingProfileModal({
  isVisible,
  onClose,
  onSelect,
}: ShippingProfileModalProps) {
  const [selectedProfile, setSelectedProfile] = useState<string | null>(null);
  const [showCustom, setShowCustom] = useState(false);

  const handleSelect = (profile: string) => {
    if (profile === "Custom") {
      setShowCustom(true);
    } else {
      setSelectedProfile(profile);
    }
  };

  const handleSave = () => {
    if (selectedProfile) {
      onSelect(selectedProfile);
      onClose();
    }
  };

  const handleCustomSave = (profile: any) => {
    // Here you would typically save the custom profile to your backend
    // For now, we'll just use the name as the profile
    onSelect(profile.name);
    onClose();
  };

  if (showCustom) {
    return (
      <Modal
        visible={isVisible}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        <CustomShippingProfile
          onBack={() => setShowCustom(false)}
          onSave={handleCustomSave}
        />
      </Modal>
    );
  }

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity onPress={() => onClose()}>
              <Text style={styles.guideText}>Guide</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Shipping Profile</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.profileList}>
            {["Custom", ...shipping_profiles].map((profile, index) => (
              <TouchableOpacity
                key={`profile-${index}-${profile}`}
                style={[
                  styles.profileItem,
                  index === 0 && styles.firstItem,
                  index === shipping_profiles.length && styles.lastItem,
                ]}
                onPress={() => handleSelect(profile)}
              >
                <View style={styles.profileItemContent}>
                  <Text style={styles.profileText}>{profile}</Text>
                  {index === 0 ? (
                    <Ionicons
                      name="chevron-forward"
                      size={20}
                      color="#8E8E93"
                    />
                  ) : (
                    <View
                      style={[
                        styles.radioButton,
                        selectedProfile === profile &&
                          styles.radioButtonSelected,
                      ]}
                    />
                  )}
                </View>
                {index !== shipping_profiles.length && (
                  <View style={styles.separator} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={styles.backButton} onPress={onClose}>
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.saveButton,
                !selectedProfile && styles.saveButtonDisabled,
              ]}
              onPress={handleSave}
              disabled={!selectedProfile}
            >
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  content: {
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: "90%",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
  },
  guideText: {
    color: "#0A84FF",
    fontSize: 17,
  },
  profileList: {
    flex: 1,
  },
  profileItem: {
    paddingHorizontal: 16,
  },
  profileItemContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
  },
  firstItem: {
    borderTopWidth: 1,
    borderTopColor: "#2C2C2E",
  },
  lastItem: {
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  profileText: {
    fontSize: 17,
    color: "#fff",
  },
  radioButton: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 2,
    borderColor: "#8E8E93",
  },
  radioButtonSelected: {
    borderColor: "#1a96d2",
    backgroundColor: "#1a96d2",
  },
  separator: {
    height: 1,
    backgroundColor: "#2C2C2E",
  },
  footer: {
    flexDirection: "row",
    padding: 16,
    gap: 8,
  },
  backButton: {
    flex: 1,
    backgroundColor: "#2C2C2E",
    paddingVertical: 16,
    borderRadius: 100,
    alignItems: "center",
  },
  backButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "600",
  },
  saveButton: {
    flex: 1,
    backgroundColor: "#1a96d2",
    paddingVertical: 16,
    borderRadius: 100,
    alignItems: "center",
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "600",
  },
});
