import React, { useState, useEffect } from "react";
import { Image, StyleSheet, View, ActivityIndicator, Text } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { getImageUrl } from "../../lib/utils";

interface UserAvatarProps {
  image?: string | null;
  size?: number;
  style?: any;
  username?: string;
  color?: string;
}

export default function UserAvatar({
  image,
  size = 40,
  style,
  username = "",
  color = "#2C2C2E",
}: UserAvatarProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);

  const handleImageError = (error: any) => {
    console.error(`Image loading error for ${username}:`, error);
    setHasError(true);
    setIsLoading(false);
  };

  const styles = StyleSheet.create({
    container: {
      width: size,
      height: size,
      borderRadius: size / 2,
      backgroundColor: color,
      overflow: "hidden",
      justifyContent: "center",
      alignItems: "center",
    },
    image: {
      width: "100%",
      height: "100%",
    },
    fallbackText: {
      color: "#FFFFFF",
      fontSize: size * 0.5,
      fontWeight: "bold",
      textTransform: "uppercase",
    },
    fallbackIcon: {
      opacity: 0.5,
      backgroundColor: color,
    },
    fallbackIconText: {
      color: "#FFFFFF",
      fontSize: size * 0.5,
      fontWeight: "bold",
      textTransform: "uppercase",
    },
  });

  const renderContent = () => {
    const imageUrl = getImageUrl(image ?? undefined);
    if (!imageUrl || hasError) {
      if (username) {
        const firstLetter = username.charAt(0);
        return <Text style={styles.fallbackText}>{firstLetter}</Text>;
      }
      return (
        <View style={styles.fallbackIcon}>
          <Text style={styles.fallbackText}>{username.charAt(0)}</Text>
        </View>
      );
    }
    return (
      <Image
        source={{ uri: getImageUrl(imageUrl) }}
        style={styles.image}
        onLoadStart={() => setIsLoading(true)}
        onLoadEnd={() => setIsLoading(false)}
        onError={handleImageError}
      />
    );
  };

  return <View style={[styles.container, style]}>{renderContent()}</View>;
}
