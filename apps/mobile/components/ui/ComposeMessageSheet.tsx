import React, { useState, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
} from "react-native";
import { CustomBottomSheet } from "./BottomSheet";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { SafetyGuidelinesSheet } from "./SafetyGuidelinesSheet";
import { ChatScreen } from "./ChatScreen";
import { useRouter } from "expo-router";
import { getImageUrl } from "apps/mobile/lib/utils";

interface ComposeMessageSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onChatCreated?: (chatId: Id<"chats">) => void;
}

// Get the first letter or @ symbol if string is empty
const getInitial = (name: string = "") => {
  return name.charAt(0).toUpperCase() || "@";
};

// UserAvatar component to handle image URL fetching
const UserAvatar = React.memo(
  ({
    image,
    username,
    color,
  }: {
    image?: string;
    username: string;
    color?: string;
  }) => {
    const imageUrl = useQuery(
      api.users.getImageUrl,
      image ? { storageId: image as Id<"_storage"> } : "skip",
    );

    if (!imageUrl) {
      return (
        <View style={[styles.userSelectedAvatar, { backgroundColor: color }]}>
          <Text style={styles.avatarInitial}>{getInitial(username)}</Text>
        </View>
      );
    }

    return (
      <Image source={{ uri: imageUrl }} style={styles.userSelectedAvatar} />
    );
  },
);

// UserListItem component to handle image URL fetching
const UserListItem = React.memo(
  ({
    user,
    isSelected,
    onSelect,
  }: {
    user: {
      _id: Id<"users">;
      name?: string;
      username?: string;
      image?: string;
      followers: number;
      color?: string;
      [key: string]: any;
    };
    isSelected: boolean;
    onSelect: () => void;
  }) => {
    const displayName = user.name || user.username || "";
    const avatarContent = user.image ? (
      <Image source={{ uri: getImageUrl(user.image) }} style={styles.userAvatar} />
    ) : (
      <View style={[styles.userAvatar, { backgroundColor: user.color }]}>
        <Text style={styles.avatarText}>{getInitial(displayName)}</Text>
      </View>
    );

    return (
      <TouchableOpacity
        style={[styles.userItem, isSelected && styles.userItemSelected]}
        onPress={onSelect}
      >
        {avatarContent}
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{displayName}</Text>
          <Text style={styles.userFollowers}>
            <Text style={{ color: "#fff" }}>{user.followers} </Text>
            follower{user.followers === 1 ? "" : "s"}
          </Text>
        </View>
      </TouchableOpacity>
    );
  },
);

export const ComposeMessageSheet: React.FC<ComposeMessageSheetProps> = ({
  isOpen,
  onClose,
  onChatCreated,
}) => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUsers, setSelectedUsers] = useState<
    Array<{
      _id: Id<"users">;
      name?: string;
      username: string;
      image?: string;
      color?: string;
    }>
  >([]);
  const [isGroup, setIsGroup] = useState(false);
  const [groupTitle, setGroupTitle] = useState("");
  const [showSafetyGuidelines, setShowSafetyGuidelines] = useState(false);
  const [createdChatId, setCreatedChatId] = useState<Id<"chats"> | null>(null);
  const [showChat, setShowChat] = useState(false);

  const currentUser = useQuery(api.users.viewer);
  const searchResults = useQuery(api.users.searchUsers, {
    searchQuery: searchQuery,
    paginationOpts: {
      numItems: 10,
      cursor: null,
    },
  });

  const createChat = useMutation(api.chat.createChat);
  const updateUserAgreement = useMutation(api.users.updateSafetyAgreement);

  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  const handleUserSelect = useCallback(
    (user: {
      _id: Id<"users">;
      name?: string;
      username: string;
      image?: string;
      color?: string;
    }) => {
      setSelectedUsers((prev) => {
        const isAlreadySelected = prev.some((u) => u._id === user._id);
        if (isAlreadySelected) {
          const newUsers = prev.filter((u) => u._id !== user._id);
          // Update isGroup state based on remaining users
          setIsGroup(newUsers.length > 1);
          return newUsers;
        }
        const newUsers = [...prev, user];
        // Update isGroup state based on new users count
        setIsGroup(newUsers.length > 1);
        setSearchQuery("");
        return newUsers;
      });
    },
    [],
  );

  const handleStartMessaging = useCallback(async () => {
    if (selectedUsers.length === 0) {
      console.warn("No users selected");
      return;
    }

    try {
      const chatId = await createChat({
        participantIds: selectedUsers.map((user) => user._id),
        title: isGroup ? groupTitle : undefined,
        isGroup: isGroup || selectedUsers.length > 1,
      });

      // Store the created chat ID
      setCreatedChatId(chatId);

      // Check if user has already agreed to safety guidelines
      if (!currentUser?.safetyGuidelinesAgreement?.agreed) {
        setShowSafetyGuidelines(true);
      } else {
        // If already agreed, navigate to chat screen
        onChatCreated?.(chatId);
        onClose();
        router.push(`/chat/${chatId}`);
      }
    } catch (error) {
      console.error("Failed to create chat:", error);
    }
  }, [
    selectedUsers,
    isGroup,
    groupTitle,
    createChat,
    onChatCreated,
    currentUser,
    router,
    onClose,
  ]);

  const handleSafetyGuidelinesAgree = useCallback(async () => {
    try {
      // Update user's safety guidelines agreement
      await updateUserAgreement();

      // Navigate to the created chat after agreeing
      if (createdChatId) {
        onChatCreated?.(createdChatId);
        onClose();
        router.push(`/chat/${createdChatId}`);
        // Reset form state
        setSelectedUsers([]);
        setSearchQuery("");
        setGroupTitle("");
        setIsGroup(false);
        // Close safety guidelines sheet
        setShowSafetyGuidelines(false);
      }
    } catch (error) {
      console.error("Failed to update safety agreement:", error);
    }
  }, [updateUserAgreement, createdChatId, onChatCreated, router, onClose]);

  const handleChatBack = useCallback(() => {
    setShowChat(false);
    setCreatedChatId(null);
    onClose();
  }, [onClose]);

  if (showChat && createdChatId) {
    return <ChatScreen chatId={createdChatId} onBack={handleChatBack} />;
  }

  return (
    <>
      <CustomBottomSheet
        isOpen={isOpen}
        onClose={onClose}
        snapPoints={["90%"]}
        enableDynamicHeight={false}
      >
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>New Message</Text>
            <TouchableOpacity onPress={onClose}>
              <Text style={styles.closeButton}>Cancel</Text>
            </TouchableOpacity>
          </View>

          {selectedUsers.length > 1 && (
            <View style={styles.groupTitleContainer}>
              <TextInput
                style={styles.groupTitleInput}
                placeholder="Group Chat Name (optional)"
                placeholderTextColor="#8E8E93"
                value={groupTitle}
                onChangeText={setGroupTitle}
              />
            </View>
          )}

          <View style={styles.searchContainer}>
            <Text style={styles.toLabel}>To:</Text>
            {selectedUsers.map((user) => (
              <TouchableOpacity
                key={user._id}
                style={styles.selectedUserChip}
                onPress={() => {}}
              >
                <UserAvatar
                  color={user?.color}
                  image={user.image}
                  username={user.name || user.username || ""}
                />
                <Text style={styles.selectedUserText}>
                  {user.name || user.username}
                </Text>
                <TouchableOpacity
                  onPress={() => handleUserSelect(user)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Text style={styles.removeUserText}>×</Text>
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
            <TextInput
              style={[
                styles.searchInput,
                selectedUsers.length > 0 && styles.searchInputWithUsers,
              ]}
              placeholder={selectedUsers.length === 0 ? "Username" : ""}
              placeholderTextColor="#8E8E93"
              value={searchQuery}
              onChangeText={handleSearchChange}
              onKeyPress={({ nativeEvent }) => {
                if (
                  nativeEvent.key === "Backspace" &&
                  searchQuery === "" &&
                  selectedUsers.length > 0
                ) {
                  const lastUser = selectedUsers[selectedUsers.length - 1];

                  if (lastUser) {
                    handleUserSelect(lastUser);
                  }
                }
              }}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.resultsContainer}>
            {searchResults?.users
              ?.filter(
                (user) =>
                  // Filter out selected users and current user
                  !selectedUsers.some(
                    (selectedUser) => selectedUser._id === user._id,
                  ) && user._id !== currentUser?._id,
              )
              .map((user) => (
                <UserListItem
                  key={user._id}
                  user={user}
                  isSelected={selectedUsers.some((u) => u._id === user._id)}
                  onSelect={() =>
                    handleUserSelect({
                      _id: user._id,
                      name: user.name,
                      username: user.username || "",
                      image: user.image,
                      color: user.color,
                    })
                  }
                />
              ))}
          </View>

          {selectedUsers.length > 0 && (
            <View style={styles.bottomButton}>
              <TouchableOpacity
                style={styles.startMessagingButton}
                onPress={handleStartMessaging}
              >
                <Text style={styles.startMessagingText}>Start messaging</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </CustomBottomSheet>

      <SafetyGuidelinesSheet
        isOpen={showSafetyGuidelines}
        onClose={() => setShowSafetyGuidelines(false)}
        onAgree={handleSafetyGuidelinesAgree}
        participantId={
          selectedUsers.length > 0
            ? (selectedUsers[0]?._id as Id<"users">)
            : undefined
        }
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#18181B",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#27272A",
  },
  title: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
  },
  closeButton: {
    fontSize: 17,
    color: "#1a96d2",
  },
  selectedUsersContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  selectedUserChip: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#27272A",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 4,
    gap: 8,
  },
  selectedUserText: {
    color: "#fff",
    fontSize: 14,
    marginRight: 4,
  },
  removeUserText: {
    color: "#8E8E93",
    fontSize: 18,
  },
  groupTitleContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#27272A",
  },
  groupTitleInput: {
    fontSize: 17,
    color: "#fff",
    padding: 8,
    backgroundColor: "#27272A",
    borderRadius: 8,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#27272A",
    gap: 8,
  },
  toLabel: {
    fontSize: 17,
    color: "#fff",
  },
  searchInput: {
    flex: 1,
    fontSize: 17,
    color: "#fff",
    padding: 0,
    minWidth: 100,
  },
  searchInputWithUsers: {
    flex: 0,
  },
  resultsContainer: {
    flex: 1,
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#27272A",
  },
  userItemSelected: {
    backgroundColor: "#27272A",
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#27272A",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  avatarText: {
    fontSize: 16,
    color: "#fff",
    textTransform: "uppercase",
    fontWeight: "600",
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 17,
    color: "#fff",
    marginBottom: 2,
  },
  userFollowers: {
    fontSize: 12,
    color: "#8E8E93",
  },
  bottomButton: {
    marginBottom: 20,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#27272A",
  },
  startMessagingButton: {
    backgroundColor: "#0A84FF",
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  startMessagingText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  userSelectedAvatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "#27272A",
    alignItems: "center",
    justifyContent: "center",
  },
  avatarInitial: {
    fontSize: 10,
    color: "#fff",
    textTransform: "uppercase",
    fontWeight: "600",
  },
});
