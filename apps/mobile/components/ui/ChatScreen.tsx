import React, { useState, useCallback, useRef, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
  Keyboard,
  KeyboardEvent,
} from "react-native";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { Ionicons } from "@expo/vector-icons";
import { ChatMessage } from "./ChatMessage";
import { ReportModal } from "./ReportModal";
import * as ImagePicker from "expo-image-picker";
import { useRouter, useLocalSearchParams } from "expo-router";

interface ChatScreenProps {
  chatId: Id<"chats">;
  onBack?: () => void;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ chatId, onBack }) => {
  const [messageText, setMessageText] = useState("");
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isReportModalVisible, setIsReportModalVisible] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<{
    id: Id<"messages">;
    senderId: Id<"users">;
  } | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const isKeyboardVisibleRef = useRef(false);
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id?: Id<"users"> }>();

  const chat = useQuery(api.chat.getChat, { chatId });
  const messages = useQuery(api.chat.getMessages, { chatId });
  const sendMessage = useMutation(api.chat.sendMessage);
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);

  const handleBack = useCallback(() => {
    if (onBack) {
      onBack();
    } else if (id) {
      // If we have a user ID, go back to their profile with the chat ID
      router.push(`/user/${id}?chatId=${chatId}`);
    } else {
      // Otherwise just go back
      router.back();
    }
  }, [onBack, id, chatId, router]);

  // Handle keyboard events
  useEffect(() => {
    const keyboardWillShow = (e: KeyboardEvent) => {
      isKeyboardVisibleRef.current = true;
      setKeyboardHeight(e.endCoordinates.height);
      // Scroll to bottom with a slight delay to ensure layout is updated
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    };

    const keyboardWillHide = () => {
      isKeyboardVisibleRef.current = false;
      setKeyboardHeight(0);
    };

    const keyboardDidShow = (e: KeyboardEvent) => {
      isKeyboardVisibleRef.current = true;
      setKeyboardHeight(e.endCoordinates.height);
      // Scroll to bottom with a slight delay to ensure layout is updated
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    };

    const keyboardDidHide = () => {
      isKeyboardVisibleRef.current = false;
      setKeyboardHeight(0);
    };

    // Add keyboard listeners
    let subscriptions = [];
    if (Platform.OS === "ios") {
      subscriptions = [
        Keyboard.addListener("keyboardWillShow", keyboardWillShow),
        Keyboard.addListener("keyboardWillHide", keyboardWillHide),
      ];
    } else {
      subscriptions = [
        Keyboard.addListener("keyboardDidShow", keyboardDidShow),
        Keyboard.addListener("keyboardDidHide", keyboardDidHide),
      ];
    }

    return () => {
      // Clean up subscriptions
      subscriptions.forEach((subscription) => subscription.remove());
    };
  }, []);

  // Request permissions when component mounts
  useEffect(() => {
    (async () => {
      const { status: cameraStatus } =
        await ImagePicker.requestCameraPermissionsAsync();
      const { status: libraryStatus } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (cameraStatus !== "granted" || libraryStatus !== "granted") {
        Alert.alert(
          "Sorry, we need camera and media library permissions to make this work!",
        );
      }
    })();
  }, []);

  const handleImageUpload = async (result: ImagePicker.ImagePickerResult) => {
    if (!result.canceled && result.assets[0]) {
      try {
        // Get the upload URL from Convex
        const uploadUrl = await generateUploadUrl({});

        // Upload the image
        const image = result.assets[0];
        const response = await fetch(uploadUrl, {
          method: "POST",
          headers: {
            "Content-Type": image.mimeType || "image/jpeg",
          },
          body: await fetch(image.uri).then((r) => r.blob()),
        });

        if (!response.ok) {
          throw new Error("Failed to upload image");
        }

        // Get the storage ID from the response
        const { storageId } = await response.json();

        // Send the message with the image
        await sendMessage({
          chatId,
          text: "",
          image: storageId,
        });
      } catch (error) {
        console.error("Failed to upload image:", error);
        Alert.alert("Error", "Failed to upload image. Please try again.");
      }
    }
  };

  const handlePickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 0.8,
      allowsEditing: true,
    });

    await handleImageUpload(result);
  };

  const handleTakePhoto = async () => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 0.8,
      allowsEditing: true,
    });

    await handleImageUpload(result);
  };

  const handleMediaButtonPress = () => {
    Alert.alert(
      "Add Photo",
      "Choose a photo from your library or take a new one",
      [
        {
          text: "Photo Library",
          onPress: handlePickImage,
        },
        {
          text: "Take Photo",
          onPress: handleTakePhoto,
        },
        {
          text: "Cancel",
          style: "cancel",
        },
      ],
    );
  };

  // Auto scroll to bottom when new messages arrive or keyboard changes
  useEffect(() => {
    if (
      scrollViewRef.current &&
      (messages?.length || isKeyboardVisibleRef.current)
    ) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  }, [messages, keyboardHeight]);

  const handleSend = useCallback(async () => {
    if (!messageText.trim()) return;

    try {
      await sendMessage({
        chatId,
        text: messageText.trim(),
      });
      setMessageText("");
      // Ensure we scroll to bottom after sending
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  }, [messageText, chatId, sendMessage]);

  const handleLongPressMessage = (
    messageId: Id<"messages">,
    senderId: Id<"users">,
  ) => {
    setSelectedMessage({ id: messageId, senderId });
    setIsReportModalVisible(true);
  };

  if (!chat || !messages) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />
        <Text style={styles.loadingText}>Loading...</Text>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={styles.keyboardView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
        >
          <View style={styles.header}>
            <TouchableOpacity onPress={handleBack} style={styles.backButton}>
              <Ionicons name="chevron-back" size={24} color="#fff" />
            </TouchableOpacity>
            <View style={styles.profileContainer}>
              {!chat.isGroup ? (
                <TouchableOpacity
                  onPress={() =>
                    router.push(`/user/${chat.otherUser?._id}?chatId=${chatId}`)
                  }
                  style={styles.userInfoButton}
                >
                  <Image
                    source={{
                      uri:
                        chat.otherUser?.image ||
                        "https://via.placeholder.com/40",
                    }}
                    style={styles.profileImage}
                  />
                  <Text style={styles.profileName} numberOfLines={1}>
                    {chat.title || chat.otherUser?.name || "Chat"}
                  </Text>
                </TouchableOpacity>
              ) : (
                <View style={styles.groupInfo}>
                  <View>
                    <Text style={styles.profileName} numberOfLines={1}>
                      {chat.title || "Group Chat"}
                    </Text>
                    <Text style={styles.memberCount}>
                      {chat.participants?.length || 0} members
                    </Text>
                  </View>
                </View>
              )}
            </View>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => {
                if (chat?.otherUser?._id) {
                  setSelectedMessage({
                    id: "" as Id<"messages">,
                    senderId: chat.otherUser._id,
                  });
                  setIsReportModalVisible(true);
                }
              }}
            >
              <Ionicons name="ellipsis-vertical" size={20} color="#fff" />
            </TouchableOpacity>
          </View>

          <ScrollView
            ref={scrollViewRef}
            style={styles.messagesContainer}
            contentContainerStyle={[
              styles.messagesContent,
              { paddingBottom: keyboardHeight > 0 ? keyboardHeight - 90 : 16 },
            ]}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={() => {
              scrollViewRef.current?.scrollToEnd({ animated: true });
            }}
            onLayout={() => {
              scrollViewRef.current?.scrollToEnd({ animated: false });
            }}
          >
            {messages?.map((message, index) => {
              const isOwnMessage = message.senderId === chat?.currentUserId;
              const showSender =
                chat?.isGroup &&
                !isOwnMessage &&
                (index === 0 ||
                  messages[index - 1]?.senderId !== message.senderId);
              const sender = chat?.participants?.find(
                (p) => p?._id === message.senderId,
              );

              return (
                <ChatMessage
                  key={message._id}
                  message={message}
                  isOwnMessage={isOwnMessage}
                  showSender={showSender}
                  senderName={sender?.name}
                  senderImage={sender?.image ?? undefined}
                  senderColor={sender?.color ?? undefined}
                  onLongPress={() =>
                    handleLongPressMessage(message._id, message.senderId)
                  }
                />
              );
            })}
          </ScrollView>

          <View
            style={[
              styles.inputContainer,
              { marginBottom: Platform.OS === "ios" ? 0 : keyboardHeight },
            ]}
          >
            <TouchableOpacity
              style={styles.mediaButton}
              onPress={handleMediaButtonPress}
            >
              <Ionicons name="add-circle-outline" size={24} color="#fff" />
            </TouchableOpacity>
            <View style={styles.textInputContainer}>
              <TextInput
                style={styles.input}
                placeholder="Message"
                placeholderTextColor="#71717A"
                value={messageText}
                onChangeText={setMessageText}
                multiline
                maxLength={1000}
              />
              <TouchableOpacity
                style={[
                  styles.sendButton,
                  messageText.trim() ? styles.sendButtonActive : null,
                ]}
                onPress={handleSend}
                disabled={!messageText.trim()}
              >
                <Ionicons
                  name="send"
                  size={20}
                  color={messageText.trim() ? "#fff" : "#71717A"}
                />
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>

      {selectedMessage && (
        <ReportModal
          isVisible={isReportModalVisible}
          onClose={() => {
            setIsReportModalVisible(false);
            setSelectedMessage(null);
          }}
          contentType={selectedMessage.id ? "message" : "user"}
          contentId={selectedMessage.id || selectedMessage.senderId}
          reportedUserId={selectedMessage.senderId}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#fff",
    fontSize: 16,
  },
  safeArea: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  keyboardView: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: "#27272A",
    backgroundColor: "#1C1C1E",
  },
  backButton: {
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
  },
  headerButton: {
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
  },
  profileContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 8,
    marginRight: 8,
  },
  userInfoButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 4,
  },
  groupInfo: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  profileImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#27272A",
    marginRight: 12,
  },
  profileName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
  },
  memberCount: {
    fontSize: 12,
    color: "#71717A",
    marginTop: 2,
  },
  messagesContainer: {
    flex: 1,
    backgroundColor: "#111",
  },
  messagesContent: {
    padding: 12,
    paddingBottom: 16,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopWidth: 0.5,
    borderTopColor: "#27272A",
    backgroundColor: "#1C1C1E",
    paddingBottom: Platform.OS === "ios" ? 24 : 12,
  },
  mediaButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  textInputContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#27272A",
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    minHeight: 40,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: "#fff",
    padding: 0,
    maxHeight: 100,
    marginRight: 8,
  },
  sendButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#27272A",
  },
  sendButtonActive: {
    backgroundColor: "#0095FF",
  },
});
