import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
} from "react";
import { View, StyleSheet } from "react-native";

type PortalContextType = {
  addPortal: (key: string, element: React.ReactNode) => void;
  removePortal: (key: string) => void;
};

const PortalContext = createContext<PortalContextType | null>(null);

export const PortalProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [portals, setPortals] = useState<{ [key: string]: React.ReactNode }>(
    {},
  );

  const actions = useMemo(
    () => ({
      addPortal: (key: string, element: React.ReactNode) => {
        setPortals((prev) => ({
          ...prev,
          [key]: element,
        }));
      },
      removePortal: (key: string) => {
        setPortals((prev) => {
          const newPortals = { ...prev };
          delete newPortals[key];
          return newPortals;
        });
      },
    }),
    [],
  );

  const portalArray = useMemo(() => Object.values(portals), [portals]);

  return (
    <PortalContext.Provider value={actions}>
      {children}
      <View style={StyleSheet.absoluteFill} pointerEvents="box-none">
        {portalArray}
      </View>
    </PortalContext.Provider>
  );
};

export const Portal: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const context = useContext(PortalContext);
  const key = React.useRef(Math.random().toString()).current;

  if (!context) {
    throw new Error("Portal must be used within a PortalProvider");
  }

  React.useEffect(() => {
    context.addPortal(key, children);
    return () => context.removePortal(key);
  }, [key, children, context]);

  return null;
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
  },
});
