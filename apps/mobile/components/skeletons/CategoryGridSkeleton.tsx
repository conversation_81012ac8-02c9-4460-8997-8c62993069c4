import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { useEffect, useRef } from "react";

export default function CategoryGridSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  // Create 4 rows of 3 items each for the grid
  const rows = Array(4).fill(null);
  const itemsPerRow = Array(3).fill(null);

  return (
    <View style={styles.container}>
      {rows.map((_, rowIndex) => (
        <View key={`row-${rowIndex}`} style={styles.gridRow}>
          {itemsPerRow.map((_, itemIndex) => (
            <View key={`item-${rowIndex}-${itemIndex}`} style={styles.card}>
              <Animated.View
                style={[styles.imageContainer, { opacity: fadeAnim }]}
              />
              <Animated.View
                style={[styles.titleSkeleton, { opacity: fadeAnim }]}
              />
              <Animated.View
                style={[styles.viewersSkeleton, { opacity: fadeAnim }]}
              />
            </View>
          ))}
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    paddingTop: 10,
  },
  gridRow: {
    flexDirection: "row",
    padding: 5,
  },
  card: {
    flex: 1,
    margin: 5,
  },
  imageContainer: {
    aspectRatio: 1,
    backgroundColor: "#2C2C2E",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  titleSkeleton: {
    height: 13,
    width: "80%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
    marginTop: 8,
    marginBottom: 4,
  },
  viewersSkeleton: {
    height: 12,
    width: "50%",
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
});
