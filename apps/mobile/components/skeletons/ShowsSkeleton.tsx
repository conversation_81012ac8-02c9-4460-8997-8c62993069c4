import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { useEffect, useRef } from "react";

export default function ShowsSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  const renderShowSkeleton = () => (
    <Animated.View style={[styles.showCard, { opacity: fadeAnim }]}>
      <View style={styles.showThumbnail} />
      <View style={styles.showInfo}>
        <View style={styles.showTitle} />
        <View style={styles.showCategory} />
      </View>
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.listContent}>
        {Array(5)
          .fill(null)
          .map((_, index) => (
            <React.Fragment key={index}>{renderShowSkeleton()}</React.Fragment>
          ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  listContent: {
    padding: 8,
  },
  showCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C1C1E",
    borderRadius: 12,
    marginBottom: 16,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#2C2C2E",
    padding: 8,
  },
  showThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: "#2C2C2E",
    marginRight: 16,
  },
  showInfo: {
    flex: 1,
    justifyContent: "center",
    gap: 8,
  },
  showTitle: {
    height: 20,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
    width: "80%",
  },
  showCategory: {
    height: 16,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
    width: "40%",
  },
});
