import React from "react";
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { useEffect, useRef } from "react";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";

export default function UserProfileSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      {/* Header Section */}
      <View style={styles.headerBackground}>
        <View style={styles.headerOverlay} />
        <View style={styles.headerContainer}>
          <TouchableOpacity style={styles.backButton}>
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
          <View style={styles.headerButtons}>
            <View style={styles.iconButton} />
            <View style={styles.iconButton} />
          </View>
        </View>
      </View>

      {/* Profile Info */}
      <View style={styles.profileInfo}>
        <Animated.View
          style={[styles.avatarContainer, { opacity: fadeAnim }]}
        />
        <Animated.View
          style={[styles.skeletonText, styles.username, { opacity: fadeAnim }]}
        />
        <Animated.View
          style={[styles.skeletonText, styles.name, { opacity: fadeAnim }]}
        />

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.stat}>
            <Animated.View
              style={[
                styles.skeletonText,
                styles.statValue,
                { opacity: fadeAnim },
              ]}
            />
            <Animated.View
              style={[
                styles.skeletonText,
                styles.statLabel,
                { opacity: fadeAnim },
              ]}
            />
          </View>
          <View style={styles.stat}>
            <Animated.View
              style={[
                styles.skeletonText,
                styles.statValue,
                { opacity: fadeAnim },
              ]}
            />
            <Animated.View
              style={[
                styles.skeletonText,
                styles.statLabel,
                { opacity: fadeAnim },
              ]}
            />
          </View>
        </View>

        {/* Bio */}
        <View style={styles.bioContainer}>
          <Animated.View
            style={[styles.skeletonText, styles.bio, { opacity: fadeAnim }]}
          />
          <Animated.View
            style={[styles.skeletonText, styles.bio, { opacity: fadeAnim }]}
          />
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Animated.View style={[styles.followButton, { opacity: fadeAnim }]} />
          <Animated.View
            style={[styles.messageButton, { opacity: fadeAnim }]}
          />
        </View>
      </View>

      {/* Tab View Skeleton */}
      <View style={styles.tabViewContainer}>
        <View style={styles.tabBar}>
          {Array(4)
            .fill(null)
            .map((_, index) => (
              <Animated.View
                key={index}
                style={[styles.tab, { opacity: fadeAnim }]}
              />
            ))}
        </View>
        <View style={styles.tabContent}>
          <View style={styles.gridContainer}>
            {Array(4)
              .fill(null)
              .map((_, index) => (
                <Animated.View
                  key={index}
                  style={[styles.gridItem, { opacity: fadeAnim }]}
                />
              ))}
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  headerBackground: {
    height: 200,
    backgroundColor: "#1a1a1a",
    position: "relative",
  },
  headerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0,0,0,0.4)",
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  headerButtons: {
    flexDirection: "row",
    gap: 8,
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  profileInfo: {
    alignItems: "center",
    paddingHorizontal: 16,
    marginTop: -50,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#333",
    marginBottom: 16,
  },
  skeletonText: {
    backgroundColor: "#333",
    borderRadius: 4,
  },
  username: {
    width: 150,
    height: 24,
    marginBottom: 8,
  },
  name: {
    width: 120,
    height: 16,
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "100%",
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#2C2C2E",
    padding: 16,
    borderRadius: 12,
    backgroundColor: "#222",
  },
  stat: {
    alignItems: "center",
    gap: 8,
    flex: 1,
  },
  statValue: {
    width: 40,
    height: 18,
  },
  statLabel: {
    width: 80,
    height: 18,
  },
  bioContainer: {
    width: "100%",
    paddingHorizontal: 16,
    marginBottom: 24,
    gap: 8,
  },
  bio: {
    width: "100%",
    height: 16,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 24,
  },
  followButton: {
    width: 120,
    height: 44,
    backgroundColor: "#333",
    borderRadius: 8,
  },
  messageButton: {
    width: 120,
    height: 44,
    backgroundColor: "#333",
    borderRadius: 8,
  },
  tabViewContainer: {
    height: 500,
  },
  tabBar: {
    flexDirection: "row",
    backgroundColor: "#2C2C2E",
    paddingVertical: 8,
  },
  tab: {
    width: Dimensions.get("window").width / 4,
    height: 32,
    backgroundColor: "#333",
    marginHorizontal: 4,
    borderRadius: 4,
  },
  tabContent: {
    padding: 16,
  },
  gridContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  gridItem: {
    width: (Dimensions.get("window").width - 48) / 2,
    height: 200,
    backgroundColor: "#333",
    borderRadius: 12,
    marginBottom: 16,
  },
});
