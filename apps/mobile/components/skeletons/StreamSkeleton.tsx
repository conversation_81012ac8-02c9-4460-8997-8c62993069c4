import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { useEffect, useRef } from "react";
import { SafeAreaView } from "react-native-safe-area-context";

export default function StreamSkeleton() {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [fadeAnim]);

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.flexBetween}>
        {/* Top Section */}
        <View style={styles.topSection}>
          <View style={styles.topLeftRow}>
            <Animated.View style={[styles.backButton, { opacity: fadeAnim }]} />
            <Animated.View
              style={[styles.usernameBar, { opacity: fadeAnim }]}
            />
          </View>
          <Animated.View style={[styles.viewerCount, { opacity: fadeAnim }]} />
        </View>
        {/* Middle Section (grows) */}
        <View style={styles.middleSection}>
          {/* Ready to Start Banner */}
          <Animated.View style={[styles.banner, { opacity: fadeAnim }]}>
            <View style={styles.bannerContent}>
              <View style={styles.bannerTime} />
              <View style={styles.bannerTitle} />
              <View style={styles.shareButton} />
            </View>
          </Animated.View>
        </View>
        {/* Bottom Section */}
        <View style={styles.bottomSection}>
          {/* Chat Area */}
          <View style={styles.chatContainer}>
            <View style={styles.chatUserRow}>
              <Animated.View
                style={[styles.userAvatar, { opacity: fadeAnim }]}
              />
              <View style={styles.chatUserTextCol}>
                <Animated.View
                  style={[styles.usernameText, { opacity: fadeAnim }]}
                />
                <Animated.View
                  style={[styles.joinedText, { opacity: fadeAnim }]}
                />
              </View>
            </View>
          </View>
          <Animated.View style={[styles.chatInputRow, { opacity: fadeAnim }]} />
          <View style={styles.bottomRow}>
            <Animated.View
              style={[styles.startShowButton, { opacity: fadeAnim }]}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    height: "100%",
  },
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    paddingHorizontal: 0,
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    height: "100%",
  },
  topSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 8,
    backgroundColor: "transparent",
    zIndex: 10,
  },
  topLeftRow: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  backButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#2C2C2E",
    marginRight: 8,
  },
  usernameBar: {
    width: 120,
    height: 20,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  viewerCount: {
    width: 60,
    height: 24,
    backgroundColor: "#2C2C2E",
    borderRadius: 12,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 8,
  },
  streamTitle: {
    width: 200,
    height: 24,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  liveIndicator: {
    width: 80,
    height: 28,
    backgroundColor: "#2C2C2E",
    borderRadius: 12,
  },
  banner: {
    backgroundColor: "rgba(34, 34, 34, 0.8)",
    borderRadius: 20,
    margin: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#3A3A3C",
  },
  bannerContent: {
    alignItems: "center",
    gap: 12,
  },
  bannerTime: {
    width: 120,
    height: 16,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  bannerTitle: {
    width: 180,
    height: 28,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
    marginVertical: 8,
  },
  shareButton: {
    width: "100%",
    height: 44,
    backgroundColor: "#2C2C2E",
    borderRadius: 22,
    marginTop: 8,
  },
  logoContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  logoImage: {
    width: 260,
    height: 260,
    borderRadius: 130,
    backgroundColor: "#2C2C2E",
  },
  chatContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  chatUserRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  userAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#2C2C2E",
    marginRight: 10,
  },
  chatUserTextCol: {
    flex: 1,
    justifyContent: "center",
    gap: 8,
  },
  usernameText: {
    width: 100,
    height: 16,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  joinedText: {
    width: 80,
    height: 16,
    backgroundColor: "#2C2C2E",
    borderRadius: 4,
  },
  chatInputRow: {
    height: 48,
    backgroundColor: "#2C2C2E",
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  bottomRow: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  startShowButton: {
    width: "100%",
    height: 48,
    backgroundColor: "#2C2C2E",
    borderRadius: 24,
  },
  bottomSection: {},
  flexBetween: {
    flex: 1,
    justifyContent: "space-between",
  },
  middleSection: {
    flex: 1,
    justifyContent: "flex-start",
  },
});
