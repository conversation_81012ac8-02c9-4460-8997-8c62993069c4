import React, { useEffect, useRef } from "react";
import { Platform } from "react-native";
import { useTrackingPermission } from "../../hooks/useTrackingPermission";

interface TrackingPermissionGuardProps {
  children: React.ReactNode;
  onPermissionResult?: (status: string) => void;
}

export const TrackingPermissionGuard: React.FC<
  TrackingPermissionGuardProps
> = ({ children, onPermissionResult }) => {
  const { requestPermission, trackingStatus } = useTrackingPermission();
  const requestedRef = useRef(false);

  useEffect(() => {
    // Skip if already requested or not needed
    if (
      requestedRef.current ||
      Platform.OS !== "ios" ||
      trackingStatus !== "not-determined"
    ) {
      return;
    }

    const requestTrackingPermission = async () => {
      try {
        // Set flag to prevent multiple requests
        requestedRef.current = true;
        const status = await requestPermission();
        onPermissionResult?.(status);
      } catch (error) {
        console.error("Error requesting tracking permission:", error);
      }
    };

    // Request permission after a short delay to ensure app is fully loaded
    const timer = setTimeout(() => {
      requestTrackingPermission();
    }, 1000);

    return () => clearTimeout(timer);
  }, [trackingStatus, requestPermission, onPermissionResult]);

  return <>{children}</>;
};
