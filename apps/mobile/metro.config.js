const { getDefaultConfig } = require("@expo/metro-config");
const path = require("path");

const projectRoot = __dirname;
const workspaceRoot = path.resolve(projectRoot, "../../");

const config = getDefaultConfig(projectRoot);

// Watch the workspace root and all packages you want to import from
config.watchFolders = [
  workspaceRoot,
  path.resolve(workspaceRoot, "packages/backend"),
  path.resolve(workspaceRoot, "packages/lib"),
  path.resolve(workspaceRoot, "packages/assets"),
];

// Map the aliases to the actual package locations
config.resolver.extraNodeModules = {
  "@workspace/backend": path.resolve(workspaceRoot, "packages/backend"),
  "@workspace/lib": path.resolve(workspaceRoot, "packages/lib"),
  "@workspace/assets": path.resolve(workspaceRoot, "packages/assets"),
};

config.resolver.nodeModulesPaths = [
  path.resolve(workspaceRoot, 'node_modules'),
  path.resolve(projectRoot, 'node_modules'),
];

module.exports = config;
