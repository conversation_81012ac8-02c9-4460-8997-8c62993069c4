import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  SafeAreaView,
  Animated,
  PanResponder,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Share,
  Linking,
  Alert,
  ActivityIndicator,
  FlatList,
} from "react-native";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useUser } from "../../hooks/useUser";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import StreamSkeleton from "../../components/skeletons/StreamSkeleton";
import PresenceTracker from "../../components/PresenceTracker";
import UserAvatar from "../../components/ui/UserAvatar";
import {
  StreamVideo,
  LivestreamPlayer,
  HostLivestream,
  StreamVideoClient,
  StreamCall,
  ViewerLivestream,
} from "@stream-io/video-react-native-sdk";
import {
  getOrCreateStreamVideoClient,
  disconnectStreamVideoClient,
} from "../../lib/streamVideo";

export default function StreamShowPage() {
  const params = useLocalSearchParams();
  const streamId = params.id as string | undefined;
  const { user } = useUser();
  const router = useRouter();
  const [message, setMessage] = React.useState("");
  const chatScrollRef = React.useRef<ScrollView>(null);
  const chatAreaRef = React.useRef<View>(null);
  const [uiVisible, setUiVisible] = React.useState(true);
  const fadeAnim = React.useRef(new Animated.Value(1)).current;
  const overlayOpacity = React.useRef(new Animated.Value(0.5)).current;
  const [showSwipeIndicator, setShowSwipeIndicator] = React.useState(false);
  const [isTouchingChatArea, setIsTouchingChatArea] = React.useState(false);

  const toggleBookmark = useMutation(api.bookmarks.toggleBookmark);
  const [optimisticBookmarked, setOptimisticBookmarked] = React.useState<
    boolean | null
  >(null);
  const hasJoinedRef = React.useRef(false);
  const joinTimerRef = React.useRef<ReturnType<typeof setTimeout> | null>(null);
  const joinTimestampRef = React.useRef(0);
  const [streamVideoData, setStreamVideoData] = React.useState<any>(null);
  const [showExplicitBanner, setShowExplicitBanner] = React.useState(false);
  const opacity = React.useRef(new Animated.Value(0)).current;
  const startStream = useMutation(api.streams.startStream);
  const [now, setNow] = React.useState(Date.now());
  const [starting, setStarting] = React.useState(false);
  const startStreamHandlerRef = React.useRef<(() => void) | null>(null);

  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: (event) => {
        if (chatAreaRef.current) {
          return false;
        }
        return true;
      },
      onMoveShouldSetPanResponder: (event, gestureState) => {
        if (
          isTouchingChatArea &&
          Math.abs(gestureState.dy) > Math.abs(gestureState.dx)
        ) {
          return false;
        }
        return (
          Math.abs(gestureState.dx) > 20 &&
          Math.abs(gestureState.dx) > Math.abs(gestureState.dy)
        );
      },
      onPanResponderTerminationRequest: () => true,
      onPanResponderGrant: () => {},
      onPanResponderMove: (_, gestureState) => {},
      onPanResponderRelease: (_, gestureState) => {
        if (Math.abs(gestureState.dx) < 5 && Math.abs(gestureState.dy) < 5) {
          if (!uiVisible) {
            setUiVisible(true);
            setShowSwipeIndicator(false);
            Animated.parallel([
              Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true,
              }),
              Animated.timing(overlayOpacity, {
                toValue: 0.5,
                duration: 200,
                useNativeDriver: false,
              }),
            ]).start();
          }
          return true;
        } else if (gestureState.dx < -40 && uiVisible) {
          setUiVisible(false);
          setShowSwipeIndicator(true);
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 0,
              duration: 200,
              useNativeDriver: true,
            }),
            Animated.timing(overlayOpacity, {
              toValue: 0.2,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start();
        } else if (gestureState.dx > 40) {
          setUiVisible(true);
          setShowSwipeIndicator(false);
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 200,
              useNativeDriver: true,
            }),
            Animated.timing(overlayOpacity, {
              toValue: 0.5,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start();
        }
        return true;
      },
      onPanResponderTerminate: () => true,
    }),
  ).current;

  React.useEffect(() => {
    let timer: ReturnType<typeof setTimeout>;
    if (!uiVisible && showSwipeIndicator) {
      timer = setTimeout(() => {
        setShowSwipeIndicator(false);
      }, 2000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [uiVisible, showSwipeIndicator]);

  const stream = useQuery(
    api.streams.getStreamDetails,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip",
  );

  const chatMessages = useQuery(
    api.streams.getChatMessages,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip",
  );

  const streamViewers = useQuery(
    api.streams.getStreamViewers,
    streamId ? { streamId: streamId as Id<"streams"> } : "skip",
  );

  React.useEffect(() => {
    if (stream && typeof stream.isBookmarked === "boolean") {
      setOptimisticBookmarked(stream.isBookmarked);
    }
  }, [stream?.isBookmarked]);

  const handleToggleBookmark = async () => {
    if (!streamId) return;
    setOptimisticBookmarked((prev) => !(prev ?? false));
    try {
      const result = await toggleBookmark({
        streamId: streamId as Id<"streams">,
      });
      setOptimisticBookmarked(result.bookmarked);
    } catch (error) {
      setOptimisticBookmarked(stream?.isBookmarked ?? false);
      console.error("Error toggling bookmark:", error);
    }
  };

  React.useEffect(() => {
    if (!streamId || !user) return;
    if (joinTimerRef.current) {
      clearTimeout(joinTimerRef.current);
      joinTimerRef.current = null;
    }
    const now = Date.now();
    if (now - joinTimestampRef.current < 10000 && hasJoinedRef.current) {
      return;
    }
    
    return () => {
      if (joinTimerRef.current) {
        clearTimeout(joinTimerRef.current);
        joinTimerRef.current = null;
      }
      if (
        hasJoinedRef.current &&
        Date.now() - joinTimestampRef.current > 3000
      ) {
        hasJoinedRef.current = false;
        leaveStream({ streamId: streamId as Id<"streams"> }).catch((error) => {
          console.error("Error leaving stream:", error);
        });
      }
    };
  }, [streamId, user?.id]);

  React.useEffect(() => {
    if (chatMessages && chatMessages.length > 0 && chatScrollRef.current) {
      setTimeout(() => {
        chatScrollRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [chatMessages]);

  const handleSendMessage = async () => {
    if (!message.trim() || !streamId || !user) return;
    try {
      await sendChatMessage({
        streamId: streamId as Id<"streams">,
        message: message.trim(),
      });
      setMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  const handleSendReaction = async (reaction: string) => {
    if (!streamId || !user) return;
    try {
      await sendReaction({
        streamId: streamId as Id<"streams">,
        reaction,
      });
    } catch (error) {
      console.error("Error sending reaction:", error);
    }
  };

  React.useEffect(() => {
    if (stream && stream.explicitContent) {
      setShowExplicitBanner(true);
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setShowExplicitBanner(false);
        opacity.setValue(0);
      });
    }
    return () => {
      opacity.stopAnimation();
      opacity.setValue(0);
    };
  }, [stream && stream.explicitContent]);

  const handleShare = async () => {
    if (!stream?._id) return;
    const appUrl = `liveciety://stream/${stream._id}`;
    const webUrl = `https://liveciety.com/share/${stream._id}`;
    try {
      const supported = await Linking.canOpenURL(appUrl);
      if (supported) {
        await Linking.openURL(appUrl);
      } else {
        await Share.share({
          message: webUrl,
          url: webUrl,
          title: "Check out this stream on Liveciety!",
        });
      }
    } catch (error) {
      await Share.share({
        message: webUrl,
        url: webUrl,
        title: "Check out this stream on Liveciety!",
      });
    }
  };

  React.useEffect(() => {
    if (streamId && getStreamVideoToken) {
      getStreamVideoToken({ streamId: streamId as Id<"streams"> }).then(
        setStreamVideoData,
      );
    }
  }, [streamId, getStreamVideoToken]);

  const streamVideoClient = React.useMemo(() => {
    if (!streamVideoData || !user) return null;
    return getOrCreateStreamVideoClient({
      apiKey: streamVideoData.apiKey,
      user: {
        id: streamVideoData.userId,
        name: user?.username || user?.name || "User",
      },
      token: streamVideoData.token,
    });
  }, [streamVideoData, user]);

  const call = React.useMemo(() => {
    if (!streamVideoClient || !streamVideoData) return null;
    return streamVideoClient.call("livestream", streamVideoData.callId);
  }, [streamVideoClient, streamVideoData]);

  React.useEffect(() => {
    const interval = setInterval(() => {
      setNow(Date.now());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  React.useEffect(() => {
    return () => {
      disconnectStreamVideoClient();
    };
  }, []);

  if (!streamId) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <Text style={{ color: "#fff", textAlign: "center", marginTop: 40 }}>
            No stream ID provided.
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  if (stream === undefined) {
    return <StreamSkeleton />;
  }
  if (!stream) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <Text style={{ color: "#fff", textAlign: "center", marginTop: 40 }}>
            Stream not found.
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  if (!streamVideoData || !streamVideoClient || !call) {
    return <StreamSkeleton />;
  }

  const logoSource = stream.thumbnailUrl
    ? { uri: stream.thumbnailUrl }
    : require("@workspace/assets/images/icon.png");

  const formatMessageTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const isModerator = (userId: Id<"users">) => {
    return stream?.moderators?.includes(userId) || false;
  };

  const isHost = (userId: Id<"users">) => {
    return stream?.seller?._id === userId;
  };

  const sortedMessages = chatMessages
    ? [...chatMessages].sort((a, b) => b.timestamp - a.timestamp)
    : [];

  const filteredMessages = [];
  const seenJoinedUsers = new Set();
  for (const msg of sortedMessages) {
    if (msg.type === "system" && msg.user?._id) {
      if (seenJoinedUsers.has(msg.user._id)) {
        continue;
      }
      seenJoinedUsers.add(msg.user._id);
    }
    filteredMessages.push(msg);
  }

  const handleStartStream = () => {
    // TODO: Implement stream logic from getstream.io
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <View style={styles.container}>
          {!!stream?.isLive ? (
            <View style={StyleSheet.absoluteFill} pointerEvents="box-none">
              <StreamVideo client={streamVideoClient}>
                <StreamCall call={call}>
                  {user && isHost(user._id) ? (
                    <HostLivestream
                      HostLivestreamTopView={() => null}
                      FollowerCount={() => null}
                      DurationBadge={() => null}
                      onStartStreamHandler={handleStartStream}
                    />
                  ) : (
                    <ViewerLivestream
                      ViewerLivestreamTopView={() => null}
                      FollowerCount={() => null}
                      DurationBadge={() => null}
                    />
                  )}
                </StreamCall>
              </StreamVideo>
            </View>
          ) : (
            <Image
              source={logoSource}
              style={[styles.backgroundImage, StyleSheet.absoluteFill]}
            />
          )}

          <Animated.View
            style={[styles.overlayGradient, { opacity: overlayOpacity }]}
          />

          {!stream?.isLive &&
            user &&
            (isHost(user._id) ? (
              <View
                style={{
                  position: "absolute",
                  top: 150,
                  left: 0,
                  right: 0,
                  alignItems: "center",
                  zIndex: 10,
                }}
              >
                <View
                  style={{
                    backgroundColor: "rgba(34,34,34,0.95)",
                    borderRadius: 20,
                    padding: 24,
                    margin: 16,
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: "#3A3A3C",
                    width: "90%",
                  }}
                >
                  <Text
                    style={{
                      color: "#8E8E93",
                      fontSize: 16,
                      marginBottom: 16,
                      textAlign: "center",
                    }}
                  >
                    Ready to start?
                  </Text>
                  <View style={{ marginBottom: 16 }}>
                    <Text
                      style={{
                        color: "#fff",
                        fontSize: 24,
                        fontWeight: "700",
                        textAlign: "center",
                      }}
                    >
                      {stream && stream.scheduledStartTime
                        ? (() => {
                            const startTime = stream.scheduledStartTime;
                            const diff = startTime - now;

                            if (diff <= 0) return "Starting now...";

                            const hours = Math.floor(diff / (1000 * 60 * 60));
                            const minutes = Math.floor(
                              (diff % (1000 * 60 * 60)) / (1000 * 60),
                            );
                            const seconds = Math.floor(
                              (diff % (1000 * 60)) / 1000,
                            );

                            return `${hours > 0 ? `${hours}h ` : ""}${minutes}m ${seconds}s`;
                          })()
                        : "Time not set"}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={{
                      backgroundColor: "#fff",
                      borderRadius: 30,
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      paddingHorizontal: 32,
                      paddingVertical: 16,
                      width: "100%",
                    }}
                    onPress={handleShare}
                  >
                    <Ionicons name="share-outline" size={20} color="#000" />
                    <Text
                      style={{
                        color: "#000",
                        fontWeight: "700",
                        fontSize: 18,
                        marginLeft: 8,
                      }}
                    >
                      Share Show
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <View
                style={{
                  position: "absolute",
                  top: 150,
                  left: 0,
                  right: 0,
                  alignItems: "center",
                  zIndex: 10,
                }}
              >
                <View
                  style={{
                    backgroundColor: "rgba(34,34,34,0.95)",
                    borderRadius: 30,
                    padding: 24,
                    margin: 16,
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: "#3A3A3C",
                    width: "90%",
                  }}
                >
                  <Text
                    style={{
                      color: "#8E8E93",
                      fontSize: 16,
                      marginBottom: 16,
                      textAlign: "center",
                    }}
                  >
                    Show Starts at{" "}
                    {stream && stream.scheduledStartTime
                      ? new Date(stream.scheduledStartTime).toLocaleTimeString(
                          [],
                          { hour: "2-digit", minute: "2-digit" },
                        )
                      : ""}
                  </Text>
                  <TouchableOpacity
                    style={{
                      backgroundColor: "#1a96da",
                      borderRadius: 30,
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      paddingHorizontal: 32,
                      paddingVertical: 16,
                      width: "100%",
                      marginBottom: 16,
                    }}
                    onPress={handleToggleBookmark}
                  >
                    <Ionicons
                      name={
                        optimisticBookmarked ? "bookmark" : "bookmark-outline"
                      }
                      size={20}
                      color="#fff"
                    />
                    <Text
                      style={{
                        color: "#fff",
                        fontWeight: "700",
                        fontSize: 18,
                        marginLeft: 8,
                      }}
                    >
                      {optimisticBookmarked
                        ? "Bookmarked"
                        : "Bookmark & Notify Me"}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={{
                      backgroundColor: "#fff",
                      borderRadius: 30,
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      paddingHorizontal: 32,
                      paddingVertical: 16,
                      width: "100%",
                    }}
                    onPress={handleShare}
                  >
                    <Ionicons name="share-outline" size={20} color="#000" />
                    <Text
                      style={{
                        color: "#000",
                        fontWeight: "700",
                        fontSize: 18,
                        marginLeft: 8,
                      }}
                    >
                      Share Show
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}

          <SafeAreaView style={styles.safeArea}>
            <Animated.View style={[styles.topSection, { opacity: fadeAnim }]}>
              <View style={styles.topLeftRow}>
                <TouchableOpacity
                  onPress={() => router.back()}
                  style={styles.backButton}
                >
                  <Ionicons
                    name="chevron-back-outline"
                    size={24}
                    color="#fff"
                  />
                </TouchableOpacity>
                {stream && stream.seller && (
                  <TouchableOpacity
                    onPress={() =>
                      stream.seller?._id &&
                      router.push(`/(tabs)/user/${stream.seller._id}`)
                    }
                    style={styles.userAvatarContainer}
                  >
                    <UserAvatar
                      imageUrl={stream.seller.image}
                      size={32}
                      username={
                        stream.seller.username || stream.seller.name || "User"
                      }
                    />
                  </TouchableOpacity>
                )}
                <Text style={styles.topUsername} numberOfLines={1}>
                  {stream
                    ? stream.seller?.username || stream.seller?.name || "User"
                    : ""}
                </Text>
              </View>
              <View style={styles.topRightRow}>
                {streamId && (
                  <PresenceTracker
                    streamId={streamId as Id<"streams">}
                    showCountOnly
                    overrideCount={stream.viewerCount}
                  />
                )}
              </View>
            </Animated.View>

            <View style={styles.gestureContainer} {...panResponder.panHandlers}>
              {showExplicitBanner && stream.seller?._id !== user?._id && (
                <View
                  style={styles.explicitBannerContainer}
                  pointerEvents="box-none"
                >
                  <Animated.View style={[styles.explicitBanner, { opacity }]}>
                    <Text style={styles.explicitBannerText}>
                      Warning: This stream contains explicit content.
                    </Text>
                  </Animated.View>
                </View>
              )}

              <Animated.View
                style={[styles.contentContainer, { opacity: fadeAnim }]}
              >
                <View
                  style={styles.chatMainContainer}
                  ref={chatAreaRef}
                  onTouchStart={() => setIsTouchingChatArea(true)}
                  onTouchEnd={() => setIsTouchingChatArea(false)}
                >
                  <FlatList
                    data={filteredMessages}
                    renderItem={({ item: msg }) => {
                      if (msg.type === "system") {
                        return (
                          <View
                            key={msg._id}
                            style={styles.joinedMessageContainer}
                          >
                            <UserAvatar
                              imageUrl={msg.user?.image}
                              size={32}
                              username={msg.user?.username || ""}
                              color={msg.user?.color || "#2C2C2E"}
                            />
                            <View style={styles.joinedMessageContent}>
                              <Text style={styles.joinedUsername}>
                                {msg.user?.username || "Anonymous"}
                              </Text>
                              <Text style={styles.joinedText}>joined 👋</Text>
                            </View>
                          </View>
                        );
                      } else {
                        return (
                          <View key={msg._id} style={styles.chatMessageRow}>
                            <UserAvatar
                              imageUrl={msg.user?.image}
                              size={32}
                              username={msg.user?.username || ""}
                              color={msg.user?.color || "#2C2C2E"}
                            />
                            <View style={styles.chatMessageContent}>
                              <View style={styles.messageHeaderRow}>
                                <Text style={styles.chatMessageUsername}>
                                  {msg.user?.username || "Anonymous"}
                                </Text>
                                {isHost(msg.userId) && (
                                  <View style={styles.hostBadge}>
                                    <Text style={styles.hostBadgeText}>
                                      Host
                                    </Text>
                                  </View>
                                )}
                                {!isHost(msg.userId) &&
                                  isModerator(msg.userId) && (
                                    <View style={styles.modBadge}>
                                      <Text style={styles.modBadgeText}>
                                        Mod
                                      </Text>
                                    </View>
                                  )}
                              </View>
                              <Text style={styles.chatMessageText}>
                                {msg.type === "reaction" ? (
                                  <Text style={styles.reactionText}>
                                    {msg.message}
                                  </Text>
                                ) : (
                                  msg.message
                                )}
                              </Text>
                            </View>
                          </View>
                        );
                      }
                    }}
                    keyExtractor={(msg) => msg._id}
                    inverted
                    contentContainerStyle={styles.chatMessagesContent}
                    style={styles.chatScrollView}
                  />

                  <View style={styles.chatInputContainer}>
                    <TextInput
                      style={styles.chatInput}
                      placeholder="Say something..."
                      placeholderTextColor="#999"
                      value={message}
                      onChangeText={setMessage}
                      multiline={false}
                      onSubmitEditing={handleSendMessage}
                      returnKeyType="send"
                    />
                  </View>
                </View>
              </Animated.View>

              {!uiVisible && showSwipeIndicator && (
                <View style={styles.swipeIndicator}>
                  <Ionicons name="chevron-forward" size={24} color="#fff" />
                  <Text style={styles.swipeText}>
                    Swipe right to show controls
                  </Text>
                </View>
              )}

              {user && isHost(user._id) && !stream?.isLive && (
                <TouchableOpacity
                  style={styles.startStreamButton}
                  onPress={handleStartStream}
                >
                  <Text style={styles.startStreamButtonText}>Start Stream</Text>
                </TouchableOpacity>
              )}
            </View>
          </SafeAreaView>
        </View>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    position: "relative",
  },
  safeArea: {
    flex: 1,
    position: "relative",
  },
  gestureContainer: {
    flex: 1,
    position: "relative",
    zIndex: 2,
  },
  backgroundImage: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: "100%",
    height: "100%",
    zIndex: 0,
  },
  overlayGradient: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(28, 28, 30, 0.5)",
    zIndex: 1,
    pointerEvents: "none", // Ensure the overlay doesn't block touches
  },
  contentContainer: {
    flex: 1,
    backgroundColor: "transparent",
    paddingHorizontal: 0,
    justifyContent: "flex-start",
    zIndex: 2,
  },
  scheduledBanner: {
    backgroundColor: "rgba(34, 34, 34, 0.8)",
    borderRadius: 20,
    margin: 16,
    padding: 20,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#3A3A3C",
    backdropFilter: "blur(10px)",
  },
  bannerSubtitle: {
    color: "#8E8E93",
    fontSize: 15,
    marginBottom: 8,
    textAlign: "center",
  },
  bannerTitle: {
    color: "#fff",
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 16,
    textAlign: "center",
  },
  saveYellowButton: {
    backgroundColor: "#1a96da",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 12,
    width: "100%",
    marginBottom: 8,
  },
  saveYellowButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 8,
  },
  shareButtonWhite: {
    backgroundColor: "#FFFFFF",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 12,
    width: "100%",
  },
  shareButtonWhiteText: {
    color: "#000",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 8,
  },
  pollContainer: {
    backgroundColor: "rgba(40, 40, 40, 0.9)",
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  pollHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  pollQuestion: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  pollOption: {
    backgroundColor: "rgba(60, 60, 60, 0.8)",
    borderRadius: 25,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#5A5A5C",
  },
  pollOptionText: {
    color: "#fff",
    fontSize: 16,
    textAlign: "center",
  },
  chatMainContainer: {
    backgroundColor: "transparent",
    marginBottom: 8,
    height: Dimensions.get("window").height * 0.33,
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
  },
  chatScrollView: {
    marginBottom: 8,
  },
  chatMessagesContent: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    paddingTop: 8,
  },
  hostMessageContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  hostAvatarContainer: {
    marginRight: 8,
  },
  hostMessageContent: {
    flex: 1,
  },
  hostNameRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  messageHeaderRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  hostUsername: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
    marginRight: 8,
  },
  hostBadge: {
    backgroundColor: "#E93560",
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  hostBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  modBadge: {
    backgroundColor: "#4A90E2",
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  modBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  hostMessageText: {
    color: "#fff",
    fontSize: 15,
  },
  joinedMessageContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 4,
  },
  joinedMessageContent: {
    marginLeft: 8,
    flexDirection: "row",
    alignItems: "center",
  },
  joinedUsername: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
    marginRight: 4,
  },
  joinedText: {
    color: "#fff",
    fontSize: 15,
  },
  chatMessageRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  chatMessageContent: {
    flex: 1,
    marginLeft: 8,
  },
  chatMessageUsername: {
    color: "#fff",
    fontSize: 15,
    fontWeight: "600",
  },
  chatMessageText: {
    color: "#fff",
    fontSize: 15,
  },
  reactionText: {
    fontSize: 20,
  },
  productInfoContainer: {
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.1)",
  },
  productTitle: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "700",
  },
  productPrice: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "700",
    marginTop: 4,
  },
  chatInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 0.5,
    borderTopColor: "rgba(100, 100, 100, 0.3)",
    // backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  chatInput: {
    flex: 1,
    backgroundColor: "rgba(34, 34, 34, 0.8)",
    borderRadius: 20,
    color: "#fff",
    fontSize: 16,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "rgba(100, 100, 100, 0.3)",
  },
  sendButton: {
    backgroundColor: "#007AFF",
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  sendButtonDisabled: {
    backgroundColor: "#333",
  },
  sendButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 8,
  },
  streamName: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "700",
  },
  liveIndicator: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#222",
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  liveText: {
    color: "#fff",
    fontSize: 16,
    marginLeft: 6,
  },
  shareButton: {
    backgroundColor: "#1a96d2",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 10,
    width: "100%",
    marginTop: 8,
  },
  shareButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 8,
  },
  explicitBanner: {
    backgroundColor: "#D7263D",
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12,
    zIndex: 101,
    minWidth: 260,
    maxWidth: "80%",
    alignSelf: "center",
  },
  explicitBannerContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 101,
    pointerEvents: "box-none",
  },
  explicitBannerText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
    textAlign: "center",
  },
  topSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    backgroundColor: "transparent",
    zIndex: 10,
  },
  topLeftRow: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    minWidth: 0,
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  userAvatarContainer: {
    marginRight: 8,
  },
  topUsername: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 18,
    maxWidth: 180,
  },
  topRightRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  chatUserRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  swipeIndicator: {
    position: "absolute",
    left: 20,
    top: "50%",
    backgroundColor: "rgba(0,0,0,0.5)",
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  swipeText: {
    color: "#fff",
    marginLeft: 5,
    fontSize: 14,
  },
  startStreamButton: {
    backgroundColor: "#1a96da",
    borderRadius: 30,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 32,
    paddingVertical: 16,
    width: "90%",
    alignSelf: "center",
    marginTop: 8,
    marginBottom: 8,
  },
  startStreamButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 18,
    marginLeft: 8,
  },
});
