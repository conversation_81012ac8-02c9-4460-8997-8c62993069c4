import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
  Modal,
} from "react-native";
import { useR<PERSON>er, <PERSON>ack, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { MediaSelectionModal } from "../../../components/modals/media-section-modal";
import { ModeratorSelectionModal } from "../../../components/modals/moderator-selection-modal";
import { RepeatsSelectorModal } from "../../../components/modals/repeats-selector-modal";
import { SellingFormatModal } from "../../../components/modals/selling-format-modal";
import { useMutation, useAction } from "convex/react";
import { api as convexApi } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { useToast } from "../../../components/ui/use-toast";
import { Toast } from "../../../components/ui/toast";
import { z } from "zod";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

interface MediaItem {
  uri?: string;
  type: "photo" | "video" | "upload";
}

// Define the form schema with Zod
const streamFormSchema = z.object({
  title: z.string().min(1, "Show name is required"),
  description: z.string(),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().optional(),
  sellingFormat: z.string().min(1, "Selling format is required"),
  repeatsOption: z.string(),
  explicitContent: z.boolean(),
  thumbnail: z
    .object({
      uri: z.string().optional(),
      type: z.enum(["photo", "video", "upload"]),
    })
    .nullable(),
  video: z
    .object({
      uri: z.string().optional(),
      type: z.enum(["photo", "video", "upload"]),
    })
    .nullable(),
  moderators: z.array(z.any()),
  scheduledDate: z.date(),
  scheduledTime: z.string(),
  visibility: z.enum(["public", "private"]),
  muteWordsEnabled: z.boolean(),
  muteWords: z.array(z.string()),
  tags: z.array(z.string()),
});

type StreamFormData = z.infer<typeof streamFormSchema>;

export default function ScheduleStreamScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [isMediaModalVisible, setIsMediaModalVisible] = useState(false);
  const [mediaType, setMediaType] = useState<"thumbnail" | "video">(
    "thumbnail",
  );
  const [isModeratorModalVisible, setIsModeratorModalVisible] = useState(false);
  const [isRepeatsModalVisible, setIsRepeatsModalVisible] = useState(false);
  const [isSellingFormatModalVisible, setIsSellingFormatModalVisible] =
    useState(false);
  const [showExplicitContentInfo, setShowExplicitContentInfo] = useState(false);

  const createStream = useAction(convexApi.streams.createStream);
  const generateUploadUrl = useMutation(convexApi.files.generateUploadUrl);
  const { toast } = useToast();

  // Initialize form with React Hook Form
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<StreamFormData>({
    resolver: zodResolver(streamFormSchema),
    defaultValues: {
      title: "",
      description: "",
      category: "",
      subcategory: "",
      sellingFormat: "",
      repeatsOption: "Does not repeat",
      visibility: "public",
      muteWordsEnabled: false,
      muteWords: [],
      tags: [],
      explicitContent: false,
      thumbnail: null,
      video: null,
      moderators: [],
      scheduledDate: new Date(),
      scheduledTime: (() => {
        const now = new Date();
        return `${String(now.getHours()).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}`;
      })(),
    },
  });

  const scheduledDate = watch("scheduledDate");
  const scheduledTime = watch("scheduledTime");
  const explicitContent = watch("explicitContent");
  const visibility = watch("visibility");

  const hours = Array.from({ length: 24 }, (_, i) =>
    String(i).padStart(2, "0"),
  );

  const minutes = Array.from({ length: 60 }, (_, i) =>
    String(i).padStart(2, "0"),
  );

  React.useEffect(() => {
    // Only update if params exist and values are different from current form values
    if (params.selectedCategory && params.selectedSubcategory) {
      const currentCategory = watch("category");
      const currentSubcategory = watch("subcategory");

      if (
        params.selectedCategory !== currentCategory ||
        params.selectedSubcategory !== currentSubcategory
      ) {
        setValue("category", params.selectedCategory as string, {
          shouldDirty: false,
        });
        setValue("subcategory", params.selectedSubcategory as string, {
          shouldDirty: false,
        });
      }
    }
  }, [params.selectedCategory, params.selectedSubcategory]);

  const handleScheduleStream = async (data: StreamFormData) => {
    try {
      // Combine date and time
      const [hours, minutes] = data.scheduledTime.split(":").map((v) => Number(v) || 0);
      const scheduledDate = new Date(data.scheduledDate);
      scheduledDate.setHours(hours as number);
      scheduledDate.setMinutes(minutes as number);

      let thumbnailStorageId = undefined;
      let videoStorageId = undefined;

      // Upload thumbnail if it exists
      if (data.thumbnail?.uri && data.thumbnail.uri.startsWith("file")) {
        // 1. Get upload URL from Convex
        const uploadUrl = await generateUploadUrl({});
        // 2. Upload the file to Convex storage
        const response = await fetch(data.thumbnail.uri);
        const blob = await response.blob();
        const uploadRes = await fetch(uploadUrl, {
          method: "POST",
          headers: { "Content-Type": blob.type },
          body: blob,
        });
        const { storageId } = await uploadRes.json();
        thumbnailStorageId = storageId;
      }

      // Upload video if it exists
      if (data.video?.uri && data.video.uri.startsWith("file")) {
        // 1. Get upload URL from Convex
        const uploadUrl = await generateUploadUrl({});
        // 2. Upload the file to Convex storage
        const response = await fetch(data.video.uri);
        const blob = await response.blob();
        const uploadRes = await fetch(uploadUrl, {
          method: "POST",
          headers: { "Content-Type": blob.type },
          body: blob,
        });
        const { storageId } = await uploadRes.json();
        videoStorageId = storageId;
      }

      // Create stream
      const streamId = await createStream({
        title: data.title,
        description: data.description,
        category: data.category,
        subcategory: data.subcategory,
        scheduledTime: scheduledDate.getTime(),
        moderatorIds: data.moderators.map((mod) => mod._id as Id<"users">),
        visibility: data.visibility,
        explicitContent: data.explicitContent,
        muteWords: data.muteWords,
        tags: data.tags,
        thumbnail: thumbnailStorageId,
      });

      toast({
        title: "Success",
        description: "Stream scheduled successfully",
      });

      // Navigate to the stream page with the new streamId
      router.replace(`/stream/${streamId}`);
    } catch (error) {
      console.error("Error scheduling stream:", error);
      toast({
        title: "Error",
        description: "Failed to schedule stream. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleMediaSelection = (
    option: "upload" | "photo" | "video" | "scan",
    uris?: string | string[],
  ) => {
    if (uris) {
      const uri = Array.isArray(uris) ? uris[0] : uris;
      const newMedia: MediaItem = {
        uri,
        type: option === "video" ? "video" : "photo",
      };

      if (mediaType === "thumbnail") {
        setValue("thumbnail", newMedia);
      } else {
        setValue("video", newMedia);
      }
    }
  };

  const handleDeleteMedia = (type: "thumbnail" | "video") => {
    if (type === "thumbnail") {
      setValue("thumbnail", null);
    } else {
      setValue("video", null);
    }
  };

  const renderMediaItem = (type: "thumbnail" | "video") => {
    const media = type === "thumbnail" ? watch("thumbnail") : watch("video");
    const title = type === "thumbnail" ? "Add a Thumbnail" : "Add a Video";
    const time = type === "video" ? "9:16" : undefined;

    if (media?.uri) {
      return (
        <View style={styles.mediaItem}>
          <Image source={{ uri: media.uri }} style={styles.mediaImage} />
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteMedia(type)}
          >
            <Ionicons name="trash-outline" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <TouchableOpacity
        style={styles.mediaUpload}
        onPress={() => {
          setMediaType(type);
          setIsMediaModalVisible(true);
        }}
      >
        <View style={styles.mediaUploadContent}>
          <Ionicons
            name={type === "thumbnail" ? "images-outline" : "videocam-outline"}
            size={32}
            color="#fff"
          />
          <Text style={styles.mediaText}>{title}</Text>
          {time && <Text style={styles.mediaTime}>{time}</Text>}
          <Text style={styles.mediaRequired}>Optional</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderMediaSection = () => {
    return (
      <View style={styles.mediaContainer}>
        {renderMediaItem("thumbnail")}
        {renderMediaItem("video")}
      </View>
    );
  };

  const renderCalendarHeader = () => {
    const monthYear = currentMonth.toLocaleString("default", {
      month: "long",
      year: "numeric",
    });

    return (
      <View style={styles.calendarHeader}>
        <TouchableOpacity
          onPress={() => {
            const newMonth = new Date(currentMonth);
            newMonth.setMonth(currentMonth.getMonth() - 1);
            setCurrentMonth(newMonth);
          }}
        >
          <Ionicons name="chevron-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.monthYearText}>{monthYear}</Text>
        <TouchableOpacity
          onPress={() => {
            const newMonth = new Date(currentMonth);
            newMonth.setMonth(currentMonth.getMonth() + 1);
            setCurrentMonth(newMonth);
          }}
        >
          <Ionicons name="chevron-forward" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
    );
  };

  const renderCalendarDays = () => {
    const days = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];
    return (
      <View style={styles.daysHeader}>
        {days.map((day, index) => (
          <Text key={index} style={styles.dayText}>
            {day}
          </Text>
        ))}
      </View>
    );
  };

  const renderCalendarDates = () => {
    const firstDay = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      1,
    );
    const lastDay = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth() + 1,
      0,
    );
    const startingDay = firstDay.getDay();
    const totalDays = lastDay.getDate();
    const weeks = Math.ceil((totalDays + startingDay) / 7);
    const today = new Date();

    const dates = [];
    let dayCount = 1;

    for (let i = 0; i < weeks; i++) {
      const week = [];
      for (let j = 0; j < 7; j++) {
        if ((i === 0 && j < startingDay) || dayCount > totalDays) {
          week.push(null);
        } else {
          const currentDate = new Date(
            currentMonth.getFullYear(),
            currentMonth.getMonth(),
            dayCount,
          );
          const isSelected =
            scheduledDate.getDate() === dayCount &&
            scheduledDate.getMonth() === currentMonth.getMonth() &&
            scheduledDate.getFullYear() === currentMonth.getFullYear();
          const isToday =
            today.getDate() === dayCount &&
            today.getMonth() === currentMonth.getMonth() &&
            today.getFullYear() === currentMonth.getFullYear();

          week.push({
            day: dayCount,
            isSelected,
            isToday,
            date: currentDate,
          });
          dayCount++;
        }
      }
      dates.push(week);
    }

    return (
      <View style={styles.calendar}>
        {dates.map((week, i) => (
          <View key={i} style={styles.week}>
            {week.map((day, j) => (
              <TouchableOpacity
                key={j}
                style={[
                  styles.dateCell,
                  day?.isSelected && styles.selectedDate,
                  day?.isToday && styles.todayDate,
                ]}
                onPress={() => {
                  if (day) {
                    const newDate = new Date(scheduledDate);
                    newDate.setFullYear(day.date.getFullYear());
                    newDate.setMonth(day.date.getMonth());
                    newDate.setDate(day.date.getDate());
                    setValue("scheduledDate", newDate);
                  }
                }}
                disabled={!day}
              >
                <Text
                  style={[
                    styles.dateCellText,
                    day?.isSelected && styles.selectedDateText,
                    day?.isToday && styles.todayDateText,
                  ]}
                >
                  {day?.day || ""}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        ))}
      </View>
    );
  };

  const renderTimeSelector = () => {
    return (
      <Modal visible={showTimePicker} animationType="slide" transparent={true}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text></Text>
              <Text style={styles.modalTitle}>Select Time</Text>
              <TouchableOpacity onPress={() => setShowTimePicker(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>

            <View style={styles.timePickerContainer}>
              <View style={styles.timePickerColumn}>
                <Text style={styles.timePickerLabel}>Hours</Text>
                <ScrollView style={styles.timePickerScroll}>
                  {hours.map((hour) => (
                    <TouchableOpacity
                      key={hour}
                      style={[
                        styles.timePickerItem,
                        scheduledTime.split(":")[0] === hour &&
                          styles.timePickerItemSelected,
                      ]}
                      onPress={() => {
                        const [_, minute] = scheduledTime.split(":");
                        setValue("scheduledTime", `${hour}:${minute}`);
                      }}
                    >
                      <Text
                        style={[
                          styles.timePickerText,
                          scheduledTime.split(":")[0] === hour &&
                            styles.timePickerTextSelected,
                        ]}
                      >
                        {hour}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              <View style={styles.timePickerSeparator}>
                <Text style={styles.timePickerSeparatorText}>:</Text>
              </View>

              <View style={styles.timePickerColumn}>
                <Text style={styles.timePickerLabel}>Minutes</Text>
                <ScrollView style={styles.timePickerScroll}>
                  {minutes.map((minute) => (
                    <TouchableOpacity
                      key={minute}
                      style={[
                        styles.timePickerItem,
                        scheduledTime.split(":")[1] === minute &&
                          styles.timePickerItemSelected,
                      ]}
                      onPress={() => {
                        const [hour, _] = scheduledTime.split(":");
                        setValue("scheduledTime", `${hour}:${minute}`);
                      }}
                    >
                      <Text
                        style={[
                          styles.timePickerText,
                          scheduledTime.split(":")[1] === minute &&
                            styles.timePickerTextSelected,
                        ]}
                      >
                        {minute}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>

            <TouchableOpacity
              style={styles.doneButton}
              onPress={() => setShowTimePicker(false)}
            >
              <Text style={styles.doneButtonText}>Done</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
        <View style={styles.header}>
          <Text></Text>
          <Text style={styles.headerTitle}>Schedule a Stream</Text>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="close" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.form}>
          <View style={styles.section}>
            {/* <Controller
              control={control}
              name="title"
              render={({ field: { onChange, value } }) => (
                <View style={styles.repeatsContainer}>
                  <View style={styles.repeatsSelector}>
                    <View style={styles.repeatsLabelContainer}>
                      {value ? (
                        <Text style={styles.repeatsLabel}>Name your show</Text>
                      ) : null}
                      <TextInput
                        style={[
                          styles.repeatsText,
                          errors.title && styles.inputError,
                        ]}
                        placeholder={value ? "" : "Name your show *"}
                        placeholderTextColor="#8E8E93"
                        value={value}
                        onChangeText={onChange}
                        maxLength={100}
                      />
                    </View>
                  </View>
                </View>
              )}
            /> */}
            {/* {errors.title && (
              <Text style={styles.errorText}>{errors.title.message}</Text>
            )} */}

            <TouchableOpacity
              style={styles.dateTimePicker}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={styles.dateTimeText}>
                {scheduledDate.toLocaleDateString()} at {scheduledTime}
              </Text>
              <Ionicons name="calendar" size={20} color="#8E8E93" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.input}
              onPress={() => setIsModeratorModalVisible(true)}
            >
              <View style={styles.moderatorSection}>
                <Text style={styles.repeatsLabel}>Add Moderators</Text>
                {watch("moderators").length > 0 && (
                  <View style={styles.selectedModerators}>
                    {watch("moderators").map((mod, index) => (
                      <Text key={mod._id} style={styles.moderatorName}>
                        {index > 0 ? ", " : ""}
                        {mod.username}
                      </Text>
                    ))}
                  </View>
                )}
              </View>
              <Ionicons name="people" size={20} color="#8E8E93" />
            </TouchableOpacity>

            <View style={styles.repeatsContainer}>
              <TouchableOpacity
                style={styles.repeatsSelector}
                onPress={() => setIsRepeatsModalVisible(true)}
              >
                <View style={styles.repeatsLabelContainer}>
                  <Text style={styles.repeatsLabel}>Repeats</Text>
                  <Text style={styles.repeatsText}>
                    {watch("repeatsOption")}
                  </Text>
                </View>
                <Ionicons name="chevron-down" size={20} color="#8E8E93" />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Media</Text>
            {renderMediaSection()}
            <Text style={styles.mediaCount}>
              Add a thumbnail and video preview to maximize your show's exposure
              on Liveciety.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Primary Category</Text>
            <Text style={styles.categoryDescription}>
              Accurately categorizing your show will help to increase its
              discoverability.
            </Text>

            <TouchableOpacity
              style={styles.input}
              onPress={() =>
                router.push({
                  pathname: "/modals/categories",
                  params: { sourceScreen: "/stream/schedule" },
                })
              }
            >
              <View style={styles.categoryContainer}>
                {watch("category") || watch("subcategory") ? (
                  <Text style={styles.repeatsLabel}>Category</Text>
                ) : null}
                <Text
                  style={[
                    styles.inputLabel,
                    watch("category") ? styles.inputValue : null,
                  ]}
                >
                  {watch("subcategory") || watch("category") || "Category *"}
                </Text>
              </View>
              <Ionicons name="chevron-down" size={20} color="#8E8E93" />
            </TouchableOpacity>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Primary Selling Format</Text>
            <Text style={styles.categoryDescription}>
              Choose the selling format that you plan to use most often in your
              show. This information helps buyers discover your show.
            </Text>

            <View style={styles.repeatsContainer}>
              <TouchableOpacity
                style={styles.repeatsSelector}
                onPress={() => setIsSellingFormatModalVisible(true)}
                activeOpacity={0.7}
              >
                <View style={styles.repeatsLabelContainer}>
                  {watch("sellingFormat") ? (
                    <Text style={styles.repeatsLabel}>
                      Primary Selling Format
                    </Text>
                  ) : null}
                  <Text
                    style={[
                      styles.repeatsText,
                      !watch("sellingFormat") && styles.placeholderText,
                    ]}
                  >
                    {watch("sellingFormat") || "Primary Selling Format"}
                  </Text>
                </View>
                <Ionicons name="chevron-down" size={20} color="#8E8E93" />
              </TouchableOpacity>
            </View>
            <View style={styles.infoContainer}>
              <Ionicons
                name="information-circle-outline"
                size={20}
                color="#8E8E93"
              />
              <Text style={styles.infoText}>
                Choose the selling format that you plan to use most often in
                your show. This information helps buyers discover your show.
              </Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Content Settings</Text>

            <View style={styles.contentSettingsContainer}>
              <View style={styles.settingContainer}>
                <View style={styles.settingRow}>
                  <View>
                    <Text style={styles.settingTitle}>Explicit content</Text>
                    <Text style={styles.settingDescription}>
                      Turn this on if your stream contains explicit content.
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.toggleButton,
                      explicitContent && styles.toggleButtonActive,
                    ]}
                    onPress={() =>
                      setValue("explicitContent", !explicitContent)
                    }
                  >
                    <View
                      style={[
                        styles.toggleCircle,
                        explicitContent && styles.toggleCircleActive,
                      ]}
                    />
                  </TouchableOpacity>
                </View>
                <TouchableOpacity
                  style={styles.learnMoreButton}
                  onPress={() => setShowExplicitContentInfo(true)}
                >
                  <Text style={styles.learnMoreText}>Learn more</Text>
                  <Ionicons name="chevron-down" size={16} color="#007AFF" />
                </TouchableOpacity>
              </View>

              <View style={styles.showDiscoverabilityContainer}>
                <View style={styles.showDiscoverabilityRow}>
                  <View>
                    <Text style={styles.settingTitle}>
                      Show Discoverability
                    </Text>
                    <Text style={styles.settingDescription}>
                      Choose how you want your show to be discovered.
                    </Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={[
                    styles.visibilityButton,
                    visibility === "public" && styles.visibilityButtonActive,
                  ]}
                  onPress={() => setValue("visibility", "public")}
                >
                  <View style={styles.visibilityIcon}>
                    <Ionicons name="globe-outline" size={24} color="#fff" />
                  </View>
                  <View style={styles.visibilityContent}>
                    <Text style={styles.visibilityTitle}>Public</Text>
                    <Text style={styles.visibilityDescription}>
                      Anyone can find and join your show
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.radioButton,
                      visibility === "public" && styles.radioButtonActive,
                    ]}
                  >
                    <View
                      style={[
                        styles.radioInner,
                        visibility === "public" && styles.radioInnerActive,
                      ]}
                    />
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.visibilityButton,
                    visibility === "private" && styles.visibilityButtonActive,
                  ]}
                  onPress={() => setValue("visibility", "private")}
                >
                  <View style={styles.visibilityIcon}>
                    <Ionicons
                      name="lock-closed-outline"
                      size={24}
                      color="#fff"
                    />
                  </View>
                  <View style={styles.visibilityContent}>
                    <Text style={styles.visibilityTitle}>Private</Text>
                    <Text style={styles.visibilityDescription}>
                      Only people with the link can join
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.radioButton,
                      visibility === "private" && styles.radioButtonActive,
                    ]}
                  >
                    <View
                      style={[
                        styles.radioInner,
                        visibility === "private" && styles.radioInnerActive,
                      ]}
                    />
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.scheduleButton,
              errors.title && styles.scheduleButtonDisabled,
            ]}
            onPress={handleSubmit(handleScheduleStream)}
          >
            <Text style={styles.scheduleButtonText}>Schedule</Text>
          </TouchableOpacity>
        </ScrollView>

        <Modal
          visible={showDatePicker}
          animationType="slide"
          transparent={true}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text></Text>
                <Text style={styles.modalTitle}>Schedule a Stream</Text>
                <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>

              {renderCalendarHeader()}
              {renderCalendarDays()}
              {renderCalendarDates()}

              <View style={styles.timeSection}>
                <Text style={styles.timeLabel}>Time</Text>
                <TouchableOpacity
                  style={styles.timeSelector}
                  onPress={() => {
                    setShowDatePicker(false);
                    setShowTimePicker(true);
                  }}
                >
                  <Text style={styles.timeText}>{scheduledTime}</Text>
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={styles.doneButton}
                onPress={() => setShowDatePicker(false)}
              >
                <Text style={styles.doneButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        <MediaSelectionModal
          isVisible={isMediaModalVisible}
          onClose={() => setIsMediaModalVisible(false)}
          onSelectOption={handleMediaSelection}
        />

        <ModeratorSelectionModal
          isVisible={isModeratorModalVisible}
          onClose={() => setIsModeratorModalVisible(false)}
          onSelectModerators={(moderators) =>
            setValue("moderators", moderators)
          }
          selectedModerators={watch("moderators")}
        />

        <RepeatsSelectorModal
          isVisible={isRepeatsModalVisible}
          onClose={() => setIsRepeatsModalVisible(false)}
          onSelect={(option) => setValue("repeatsOption", option)}
          selectedOption={watch("repeatsOption")}
        />

        <SellingFormatModal
          isVisible={isSellingFormatModalVisible}
          onClose={() => setIsSellingFormatModalVisible(false)}
          onSelect={(option) => setValue("sellingFormat", option)}
          selectedOption={watch("sellingFormat")}
        />

        <Modal
          visible={showExplicitContentInfo}
          animationType="slide"
          transparent={true}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text></Text>
                <Text style={styles.modalTitle}>Explicit Content</Text>
                <TouchableOpacity
                  onPress={() => setShowExplicitContentInfo(false)}
                >
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.modalBody}>
                <Text style={styles.explicitContentText}>
                  While you are permitted to use explicit language on Liveciety
                  if this is toggled on, any language directed towards an
                  individual that is violent, sexually inappropriate, or hateful
                  is strictly prohibited.
                </Text>
                <Text style={[styles.explicitContentText, styles.marginTop]}>
                  Visually explicit content (nudity, sexually suggestive) is not
                  allowed. For the Comic Books & Manga categories, it is
                  allowed, however, preview videos & thumbnails should not
                  contain explicit content as outlined here.
                </Text>
                <Text style={[styles.explicitContentText, styles.marginTop]}>
                  Violation of any of these guidelines may result in your
                  removal from Liveciety.
                </Text>
              </ScrollView>
            </View>
          </View>
        </Modal>

        {renderTimeSelector()}
      </KeyboardAvoidingView>
      <Toast />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 16,
  },
  form: {
    padding: 16,
  },
  input: {
    backgroundColor: "#222",
    borderRadius: 12,
    padding: 16,
    color: "#fff",
    fontSize: 17,
    marginBottom: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  inputLabel: {
    color: "#8E8E93",
    fontSize: 17,
  },
  dateTimePicker: {
    backgroundColor: "#222",
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#2C2C2E",
    marginBottom: 16,
  },
  dateTimeText: {
    color: "#fff",
    fontSize: 17,
  },
  repeatsContainer: {
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#2C2C2E",
    backgroundColor: "#222",
    borderRadius: 12,
    padding: 16,
    zIndex: 1000,
  },
  repeatsSelector: {
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  repeatsLabelContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  repeatsLabel: {
    color: "#8E8E93",
    fontSize: 12,
  },
  repeatsText: {
    color: "#fff",
    fontSize: 17,
  },
  mediaContainer: {
    flexDirection: "row",
    gap: 16,
  },
  mediaItem: {
    flex: 1,
    height: 160,
    borderRadius: 12,
    overflow: "hidden",
    position: "relative",
  },
  mediaImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  deleteButton: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 12,
    padding: 8,
  },
  mediaUpload: {
    flex: 1,
    height: 160,
    backgroundColor: "#222",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#2C2C2E",
    borderStyle: "dashed",
  },
  mediaUploadContent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  mediaText: {
    color: "#fff",
    fontSize: 15,
    marginTop: 8,
  },
  mediaTime: {
    color: "#fff",
    fontSize: 15,
    marginTop: 4,
  },
  mediaRequired: {
    color: "#8E8E93",
    fontSize: 13,
    marginTop: 4,
  },
  mediaCount: {
    color: "#8E8E93",
    fontSize: 15,
    marginTop: 8,
  },
  categoryContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
  },
  categoryDescription: {
    color: "#8E8E93",
    fontSize: 15,
    marginBottom: 16,
  },
  recentCategories: {
    marginBottom: 16,
  },
  recentLabel: {
    color: "#8E8E93",
    fontSize: 15,
    marginBottom: 8,
  },
  categoryTags: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  categoryTag: {
    backgroundColor: "#2C2C2E",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  categoryTagText: {
    color: "#fff",
    fontSize: 15,
  },
  scheduleButton: {
    backgroundColor: "#1a96d2",
    borderRadius: 20,
    padding: 16,
    alignItems: "center",
    marginBottom: 32,
  },
  scheduleButtonDisabled: {
    opacity: 0.5,
  },
  scheduleButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "600",
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
  },
  calendarHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  monthYearText: {
    fontSize: 17,
    color: "#fff",
    fontWeight: "600",
  },
  daysHeader: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 10,
  },
  dayText: {
    color: "#8E8E93",
    fontSize: 13,
    width: 40,
    textAlign: "center",
  },
  calendar: {
    marginBottom: 20,
  },
  week: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 10,
  },
  dateCell: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
  },
  dateCellText: {
    color: "#fff",
    fontSize: 17,
  },
  selectedDate: {
    backgroundColor: "#007AFF",
  },
  selectedDateText: {
    color: "#fff",
    fontWeight: "600",
  },
  todayDate: {
    backgroundColor: "rgba(0, 122, 255, 0.1)",
  },
  todayDateText: {
    color: "#007AFF",
  },
  timeSection: {
    marginTop: 20,
    marginBottom: 20,
  },
  timeLabel: {
    color: "#fff",
    fontSize: 15,
    marginBottom: 8,
  },
  timeSelector: {
    backgroundColor: "#2C2C2E",
    borderRadius: 12,
    padding: 16,
    alignItems: "flex-end",
  },
  timeText: {
    color: "#fff",
    fontSize: 17,
  },
  doneButton: {
    alignItems: "flex-end",
    padding: 16,
  },
  doneButtonText: {
    color: "#007AFF",
    fontSize: 17,
    fontWeight: "600",
  },
  moderatorSection: {
    flex: 1,
  },
  selectedModerators: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  moderatorName: {
    color: "#fff",
    fontSize: 15,
  },
  inputValue: {
    color: "#fff",
  },
  sectionDescription: {
    color: "#8E8E93",
    fontSize: 15,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  requiredText: {
    color: "#FF3B30",
    fontSize: 12,
  },
  formatSelectorContainer: {
    backgroundColor: "#111",
    borderWidth: 1,
    borderColor: "#2C2C2E",
    borderRadius: 12,
  },
  formatSelector: {
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#222",
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  formatSelectorText: {
    color: "#fff",
    fontSize: 17,
  },
  infoContainer: {
    marginTop: -30,
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 8,
    paddingTop: 25,
    paddingHorizontal: 16,
    marginLeft: 4,
    marginRight: 4,
    backgroundColor: "#111",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  infoText: {
    color: "#8E8E93",
    fontSize: 15,
    flex: 1,
  },
  selectLimit: {
    color: "#8E8E93",
    fontSize: 12,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 16,
  },
  tagButton: {
    backgroundColor: "#2C2C2E",
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  tagText: {
    color: "#fff",
    fontSize: 15,
  },
  tagsSelector: {
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#222",
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  tagsSelectorText: {
    color: "#8E8E93",
    fontSize: 17,
  },
  shippingDescription: {
    color: "#8E8E93",
    fontSize: 15,
    marginBottom: 16,
  },
  seeMoreButton: {
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  seeMoreText: {
    color: "#007AFF",
    fontSize: 17,
  },
  placeholderText: {
    color: "#8E8E93",
  },
  contentSettingsContainer: {
    padding: 0,
  },
  settingContainer: {
    marginBottom: 24,
    overflow: "hidden",
  },
  settingRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
  },
  settingTitle: {
    color: "#fff",
    fontSize: 17,
    marginBottom: 4,
  },
  settingDescription: {
    color: "#8E8E93",
    fontSize: 15,
    maxWidth: "90%",
  },
  toggleButton: {
    width: 51,
    height: 31,
    backgroundColor: "#39393D",
    borderRadius: 16,
    padding: 2,
  },
  toggleButtonActive: {
    backgroundColor: "#34C759",
  },
  toggleCircle: {
    width: 27,
    height: 27,
    backgroundColor: "#FFFFFF",
    borderRadius: 14,
  },
  toggleCircleActive: {
    transform: [{ translateX: 20 }],
  },
  learnMoreButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingBottom: 16,
  },
  learnMoreText: {
    color: "#007AFF",
    fontSize: 15,
    marginRight: 4,
  },
  mutedWordsButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderWidth: 1,
    borderColor: "#2C2C2E",
    borderRadius: 12,
    backgroundColor: "#222",
    marginBottom: 16,
  },
  mutedWordsButtonText: {
    color: "#8E8E93",
    fontSize: 17,
  },
  languageSelector: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#2C2C2E",
  },
  languageSelectorText: {
    color: "#8E8E93",
    fontSize: 12,
    marginBottom: 4,
  },
  requiredStar: {
    color: "#FF3B30",
  },
  languageValue: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  languageValueText: {
    color: "#fff",
    fontSize: 17,
  },
  visibilityButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#2C2C2E",
  },
  visibilityButtonActive: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    overflow: "hidden",
  },
  visibilityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2C2E",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  visibilityContent: {
    flex: 1,
  },
  visibilityTitle: {
    color: "#fff",
    fontSize: 17,
    marginBottom: 2,
  },
  visibilityDescription: {
    color: "#8E8E93",
    fontSize: 13,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#8E8E93",
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  },
  radioButtonActive: {
    borderColor: "#007AFF",
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "transparent",
  },
  radioInnerActive: {
    backgroundColor: "#007AFF",
  },
  modalBody: {
    padding: 16,
  },
  explicitContentText: {
    color: "#fff",
    fontSize: 15,
    lineHeight: 22,
  },
  marginTop: {
    marginTop: 16,
  },
  showDiscoverabilityContainer: {
    backgroundColor: "#222",
    borderWidth: 1,
    borderColor: "#2C2C2E",
    borderRadius: 12,
  },
  showDiscoverabilityRow: {
    padding: 16,
  },
  timePickerContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 20,
  },
  timePickerColumn: {
    flex: 1,
    height: 200,
  },
  timePickerLabel: {
    color: "#8E8E93",
    fontSize: 13,
    textAlign: "center",
    marginBottom: 8,
  },
  timePickerScroll: {
    height: 200,
  },
  timePickerItem: {
    height: 44,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
  },
  timePickerItemSelected: {
    backgroundColor: "rgba(0, 122, 255, 0.1)",
  },
  timePickerText: {
    color: "#fff",
    fontSize: 20,
  },
  timePickerTextSelected: {
    color: "#007AFF",
    fontWeight: "600",
  },
  timePickerSeparator: {
    width: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  timePickerSeparatorText: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "600",
  },
  inputError: {
    borderColor: "#FF3B30",
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 12,
    marginTop: 4,
    marginLeft: 16,
  },
});
