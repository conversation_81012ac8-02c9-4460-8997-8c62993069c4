import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Switch,
  Image,
  FlatList,
  Modal,
} from "react-native";
import { useRouter, <PERSON>ack, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { MediaSelectionModal } from "../../../components/modals/media-section-modal";
import ShowSelectModal from "../../../components/modals/show-select-modal";
import ShippingProfileModal from "../../../components/modals/shipping-profile-modal";

interface MediaItem {
  uri?: string;
  type: "photo" | "video" | "upload";
  isCover?: boolean;
}

interface Variant {
  title: string;
  quantity: string;
  color: string;
  size: string;
}

export default function NewStreamScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const addProduct = useMutation(api.sellers.addProduct);
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);

  const [isMediaModalVisible, setIsMediaModalVisible] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaItem[]>([]);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [category, setCategory] = useState("");
  const [subcategory, setSubcategory] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [price, setPrice] = useState("");
  const [flashSale, setFlashSale] = useState(false);
  const [acceptOffers, setAcceptOffers] = useState(true);
  const [reserveForLive, setReserveForLive] = useState(false);
  const [shippingProfile, setShippingProfile] = useState("");
  const [hasHazardousMaterials, setHasHazardousMaterials] = useState(false);
  const [isEditingQuantity, setIsEditingQuantity] = useState(false);
  const [variants, setVariants] = useState<Variant[]>([]);
  const [showAuctionModal, setShowAuctionModal] = useState(false);
  const [selectedPricingType, setSelectedPricingType] = useState<
    "buyItNow" | "auction"
  >("buyItNow");
  const [startingBid, setStartingBid] = useState("1");
  const [suddenDeath, setSuddenDeath] = useState(false);
  const [selectedShow, setSelectedShow] = useState("");
  const [isShowSelectModalVisible, setIsShowSelectModalVisible] =
    useState(false);
  const [isShippingProfileModalVisible, setIsShippingProfileModalVisible] =
    useState(false);

  useEffect(() => {
    if (params.selectedCategory && params.selectedSubcategory) {
      setCategory(params.selectedCategory as string);
      setSubcategory(params.selectedSubcategory as string);
    }
  }, [params.selectedCategory, params.selectedSubcategory]);

  useEffect(() => {
    if (params.savedVariants) {
      try {
        const savedVariants = JSON.parse(params.savedVariants as string);
        if (Array.isArray(savedVariants)) {
          setVariants(savedVariants);

          const total = savedVariants.reduce(
            (sum, variant) => sum + (parseInt(variant.quantity) || 0),
            0,
          );
          setQuantity(total);
        }
      } catch (e) {
        console.error("Failed to parse saved variants:", e);
      }
    }
  }, [params.savedVariants]);

  const handlePublishProduct = async () => {
    try {
      const imageStorageIds = await Promise.all(
        selectedMedia
          .filter((media) => media.uri)
          .map(async (media) => {
            if (!media.uri) return null;

            const uploadUrl = await generateUploadUrl();

            const response = await fetch(media.uri);
            const blob = await response.blob();
            const uploadRes = await fetch(uploadUrl, {
              method: "POST",
              headers: { "Content-Type": blob.type },
              body: blob,
            });

            const { storageId } = await uploadRes.json();
            return storageId;
          }),
      );

      const validStorageIds = imageStorageIds
        .filter((id): id is string => id !== null)
        .map((id) => id as Id<"_storage">);

      await addProduct({
        name: title,
        description: description || "",
        price:
          selectedPricingType === "buyItNow"
            ? price
              ? parseFloat(price)
              : 0
            : parseFloat(startingBid),
        currency: "usd",
        inventory: quantity,
        category: category || "Uncategorized",
        subcategory: subcategory || "Uncategorized",
        condition: "new",
        images: validStorageIds,
        streamId: undefined,
        flashSale: flashSale,
        acceptOffers: acceptOffers,
        reserveForLive: reserveForLive,
        shippingProfile: shippingProfile,
        hasHazardousMaterials: hasHazardousMaterials,
        variants: variants.map((variant) => ({
          color: variant.color,
          size: variant.size,
          quantity: parseInt(variant.quantity),
        })),
        quantity: quantity,
        isAuction: selectedPricingType === "auction",
        startingBid:
          selectedPricingType === "auction"
            ? parseFloat(startingBid)
            : undefined,
        suddenDeath: suddenDeath,
        visibility: "published",
      });

      router.replace("/(tabs)");
    } catch (error) {
      console.error("Error publishing product:", error);
    }
  };

  const handleQuantityChange = (increment: boolean) => {
    setQuantity((prev) => Math.max(1, increment ? prev + 1 : prev - 1));
  };

  const handleQuantityInput = (value: string) => {
    const numValue = parseInt(value);
    if (!isNaN(numValue)) {
      setQuantity(Math.max(1, numValue));
    } else if (value === "") {
      setQuantity(1);
    }
  };

  const handleMediaSelection = (
    option: "upload" | "photo" | "video" | "scan",
    uris?: string | string[],
  ) => {
    setIsMediaModalVisible(false);
    if (uris) {
      // Handle array of URIs from camera
      if (Array.isArray(uris)) {
        const newMedia = uris.map((imageUri, index) => ({
          uri: imageUri,
          type: "photo" as const,
          isCover: selectedMedia.length === 0 && index === 0, // First image is cover by default
        }));
        setSelectedMedia((prev) => [...prev, ...newMedia]);
      }
      // Handle single URI from other sources
      else {
        const newMedia: MediaItem = {
          uri: uris,
          type: option === "video" ? "video" : "photo",
          isCover: selectedMedia.length === 0, // First image is cover by default
        };
        setSelectedMedia((prev) => [...prev, newMedia]);
      }
    }
  };

  const handleDeleteMedia = (index: number) => {
    setSelectedMedia((prev) => {
      const newMedia = prev.filter((_, i) => i !== index);
      // If we deleted the cover photo, make the first remaining photo the cover
      if (prev[index]?.isCover && newMedia.length > 0 && newMedia[0]) {
        newMedia[0].isCover = true;
      }
      return newMedia;
    });
  };

  const handleSetCover = (index: number) => {
    setSelectedMedia((prev) =>
      prev.map((item, i) => ({
        ...item,
        isCover: i === index,
      })),
    );
  };

  const renderMediaSection = () => {
    if (selectedMedia.length > 0) {
      return (
        <View style={styles.mediaContainer}>
          <FlatList
            data={[...selectedMedia, { type: "upload" as const }]}
            renderItem={({ item, index }) => {
              if (item.type === "upload") {
                return (
                  <TouchableOpacity
                    style={styles.mediaUploadSmall}
                    onPress={() => setIsMediaModalVisible(true)}
                  >
                    <View style={styles.mediaUploadContent}>
                      <Ionicons name="images-outline" size={32} color="#fff" />
                      <Text style={styles.mediaText}>Photos / Videos</Text>
                      <Text
                        style={[
                          styles.mediaRequired,
                          styles.mediaRequiredSmall,
                        ]}
                      >
                        1 photo required *
                      </Text>
                    </View>
                  </TouchableOpacity>
                );
              }
              return (
                <View style={styles.mediaItem}>
                  <Image source={{ uri: item.uri }} style={styles.mediaImage} />
                  {item.isCover && (
                    <View style={styles.coverBadge}>
                      <Text style={styles.coverText}>Cover Photo</Text>
                    </View>
                  )}
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => handleDeleteMedia(index)}
                  >
                    <Ionicons name="trash-outline" size={20} color="#fff" />
                  </TouchableOpacity>
                </View>
              );
            }}
            keyExtractor={(item, index) =>
              item.type === "upload" ? "upload" : index.toString()
            }
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.mediaList}
            contentContainerStyle={styles.mediaListContent}
          />
        </View>
      );
    }

    return (
      <TouchableOpacity
        style={styles.mediaUpload}
        onPress={() => setIsMediaModalVisible(true)}
      >
        <Ionicons name="images-outline" size={32} color="#fff" />
        <Text style={styles.mediaText}>Photos / Videos</Text>
        <Text style={styles.mediaRequired}>1 photo required *</Text>
      </TouchableOpacity>
    );
  };

  const renderVariantsList = () => {
    if (variants.length === 0) return null;

    return (
      <View style={styles.variantsList}>
        {variants.map((variant, index) => (
          <View key={index} style={styles.variantItem}>
            <Text style={styles.variantText}>
              {variant.title} (Qty: {variant.quantity})
            </Text>
            {index < variants.length - 1 && (
              <View style={styles.variantSeparator} />
            )}
          </View>
        ))}
      </View>
    );
  };

  const handlePricingTypeSelect = (type: "buyItNow" | "auction") => {
    if (type === "auction" && variants.length > 0) {
      setShowAuctionModal(true);
    } else {
      setSelectedPricingType(type);
    }
  };

  const renderPricingSection = () => {
    if (selectedPricingType === "auction") {
      return (
        <>
          <View style={styles.auctionInfoBox}>
            <Text style={styles.auctionInfoText}>
              Auctions outside of live are in beta. We'll let you know when they
              are available.
            </Text>
          </View>

          <TextInput
            style={styles.input}
            placeholder="Starting Bid*"
            placeholderTextColor="#8E8E93"
            value={startingBid}
            onChangeText={setStartingBid}
            keyboardType="decimal-pad"
          />

          <View style={styles.toggleOption}>
            <View>
              <Text style={styles.toggleLabel}>Sudden Death</Text>
            </View>
            <Switch
              value={suddenDeath}
              onValueChange={setSuddenDeath}
              trackColor={{ false: "#3A3A3C", true: "#34C759" }}
            />
          </View>
          <Text style={styles.toggleDescription}>
            This means when you're down to 00:01, the last person to bid wins!
          </Text>

          <View style={styles.toggleOption}>
            <View>
              <Text style={styles.toggleLabel}>Reserve for Live</Text>
            </View>
            <Switch
              value={reserveForLive}
              onValueChange={setReserveForLive}
              trackColor={{ false: "#3A3A3C", true: "#34C759" }}
            />
          </View>
          <Text style={styles.toggleDescription}>
            Turn this on to make this product only purchaseable within a show.
          </Text>

          {reserveForLive && (
            <TouchableOpacity
              style={styles.input}
              onPress={() => setIsShowSelectModalVisible(true)}
            >
              <Text
                style={[
                  styles.inputLabel,
                  selectedShow ? styles.inputValue : null,
                ]}
              >
                {selectedShow || "Select Show*"}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#8E8E93" />
            </TouchableOpacity>
          )}
        </>
      );
    }

    return (
      <>
        <View style={[styles.input, styles.priceInputContainer]}>
          <Text style={styles.currencySymbol}>$</Text>
          <TextInput
            style={styles.priceInput}
            placeholder="Price *"
            placeholderTextColor="#8E8E93"
            value={price}
            onChangeText={setPrice}
            keyboardType="decimal-pad"
          />
        </View>

        <View style={styles.toggleOption}>
          <View>
            <Text style={styles.toggleLabel}>Flash Sale</Text>
            <View style={styles.newBadge}>
              <Text style={styles.newBadgeText}>New</Text>
            </View>
          </View>
          <Switch
            value={flashSale}
            onValueChange={setFlashSale}
            trackColor={{ false: "#3A3A3C", true: "#34C759" }}
          />
        </View>
        <Text style={styles.toggleDescription}>
          Turn this on to enable flash sale on this product.
        </Text>

        <View style={styles.toggleOption}>
          <View>
            <Text style={styles.toggleLabel}>Accept Offers</Text>
          </View>
          <Switch
            value={acceptOffers}
            onValueChange={setAcceptOffers}
            trackColor={{ false: "#3A3A3C", true: "#34C759" }}
          />
        </View>
        <Text style={styles.toggleDescription}>
          Turn this on to accept offers. You can accept, counter or decline the
          offers.
        </Text>
      </>
    );
  };

  const handleShippingProfileSelect = (profile: string) => {
    setShippingProfile(profile);
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
        <View style={styles.header}>
          <Text></Text>
          <Text style={styles.headerTitle}>Configure listing</Text>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="close" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.form}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Media</Text>
            {renderMediaSection()}
            <Text style={styles.mediaCount}>
              Photos: {selectedMedia.filter((m) => m.type === "photo").length}/8
              • Video: {selectedMedia.filter((m) => m.type === "video").length}
              /1
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Product Details</Text>
            <TouchableOpacity
              style={styles.input}
              onPress={() =>
                router.push({
                  pathname: "/modals/categories",
                  params: { sourceScreen: "/stream/new-listing" },
                })
              }
            >
              <Text
                style={[styles.inputLabel, category ? styles.inputValue : null]}
              >
                {subcategory || category || "Category*"}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#8E8E93" />
            </TouchableOpacity>

            <TextInput
              style={styles.input}
              placeholder="Title*"
              placeholderTextColor="#8E8E93"
              value={title}
              onChangeText={setTitle}
              maxLength={100}
            />

            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Description*"
              placeholderTextColor="#8E8E93"
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              maxLength={500}
            />

            <View style={styles.quantityContainer}>
              <Text style={styles.quantityLabel}>Quantity Available</Text>
              <View
                style={[
                  styles.quantityControls,
                  variants.length > 0 && { opacity: 0.5 },
                ]}
              >
                <TouchableOpacity
                  onPress={() => handleQuantityChange(false)}
                  disabled={variants.length > 0}
                >
                  <Text style={styles.quantityButton}>-</Text>
                </TouchableOpacity>
                {isEditingQuantity ? (
                  <TextInput
                    style={styles.quantityInput}
                    value={quantity.toString()}
                    onChangeText={handleQuantityInput}
                    keyboardType="number-pad"
                    onBlur={() => setIsEditingQuantity(false)}
                    autoFocus
                    selectTextOnFocus
                    editable={variants.length === 0}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={() => setIsEditingQuantity(true)}
                    disabled={variants.length > 0}
                  >
                    <Text style={styles.quantityValue}>{quantity}</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  onPress={() => handleQuantityChange(true)}
                  disabled={variants.length > 0}
                >
                  <Text style={styles.quantityButton}>+</Text>
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity
              style={styles.variantsButton}
              onPress={() =>
                router.push({
                  pathname: "/modals/variants",
                  params: { variants: JSON.stringify(variants) },
                })
              }
            >
              <View style={styles.variantsContent}>
                <Text style={styles.variantsButtonText}>Variants</Text>
                <Text style={styles.variantsDescription}>
                  Add various colors or sizes, and quantities for this item.
                </Text>
                {renderVariantsList()}
              </View>
              <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
            </TouchableOpacity>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pricing</Text>
            <View style={styles.pricingOptions}>
              <TouchableOpacity
                style={[
                  styles.pricingOption,
                  selectedPricingType === "buyItNow" &&
                    styles.pricingOptionSelected,
                ]}
                onPress={() => handlePricingTypeSelect("buyItNow")}
              >
                <Text
                  style={[
                    styles.pricingOptionText,
                    selectedPricingType === "buyItNow" &&
                      styles.pricingOptionTextSelected,
                  ]}
                >
                  Buy It Now
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.pricingOption,
                  selectedPricingType === "auction" &&
                    styles.pricingOptionSelected,
                ]}
                onPress={() => handlePricingTypeSelect("auction")}
              >
                <Text
                  style={[
                    styles.pricingOptionText,
                    selectedPricingType === "auction" &&
                      styles.pricingOptionTextSelected,
                  ]}
                >
                  Auction
                </Text>
              </TouchableOpacity>
            </View>
            {renderPricingSection()}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Shipping</Text>
            <TouchableOpacity
              style={styles.input}
              onPress={() => setIsShippingProfileModalVisible(true)}
            >
              <Text
                style={[
                  styles.inputLabel,
                  shippingProfile ? styles.inputValue : null,
                ]}
              >
                {shippingProfile || "Shipping Profile*"}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#8E8E93" />
            </TouchableOpacity>

            <View style={styles.toggleOption}>
              <View>
                <Text style={styles.toggleLabel}>Hazardous Materials</Text>
              </View>
              <Switch
                value={hasHazardousMaterials}
                onValueChange={setHasHazardousMaterials}
                trackColor={{ false: "#3A3A3C", true: "#34C759" }}
              />
            </View>
            <Text style={styles.toggleDescription}>
              Carriers restrict shipping items that may pose risks to safety,
              like lithium batteries.
            </Text>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity style={styles.draftButton}>
              <Text style={styles.draftButtonText}>Save Draft</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.footerPublishButton,
                !title && styles.footerPublishButtonDisabled,
              ]}
              onPress={handlePublishProduct}
              disabled={!title}
            >
              <Text style={styles.footerPublishButtonText}>Publish</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.premierNote}>
            <Text style={styles.premierNoteText}>
              Complete key information fields to create a quality listing that
              counts towards your Premier Shop status.
            </Text>
            <TouchableOpacity>
              <Text style={styles.learnMoreText}>Learn more</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        <MediaSelectionModal
          isVisible={isMediaModalVisible}
          onClose={() => setIsMediaModalVisible(false)}
          onSelectOption={handleMediaSelection}
        />

        <Modal
          visible={showAuctionModal}
          transparent={true}
          animationType="fade"
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Auctions Unavailable</Text>
              <Text style={styles.modalText}>
                The auction sales format is not available when using variants.
                Please remove the variation options to list this item as an
                auction.
              </Text>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowAuctionModal(false)}
              >
                <Text style={styles.modalButtonText}>OK</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        <ShowSelectModal
          isVisible={isShowSelectModalVisible}
          onClose={() => setIsShowSelectModalVisible(false)}
        />

        <ShippingProfileModal
          isVisible={isShippingProfileModalVisible}
          onClose={() => setIsShippingProfileModalVisible(false)}
          onSelect={handleShippingProfileSelect}
        />
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2C2C2E",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
  },
  headerPublishButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#0A84FF",
    borderRadius: 16,
  },
  headerPublishButtonDisabled: {
    backgroundColor: "#0A84FF44",
  },
  headerPublishButtonText: {
    color: "#fff",
    fontWeight: "600",
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 16,
  },
  mediaContainer: {
    flexDirection: "row",
    gap: 16,
    marginBottom: 12,
    width: "100%",
  },
  mediaWrapper: {
    flex: 1,
  },
  mediaList: {
    flex: 1,
  },
  mediaListContent: {
    gap: 16,
  },
  mediaItem: {
    width: 160,
    aspectRatio: 1,
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: "#2C2C2E",
    position: "relative",
  },
  mediaImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  coverBadge: {
    position: "absolute",
    top: 12,
    left: 12,
    backgroundColor: "#0A84FF",
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
  },
  coverText: {
    color: "#fff",
    fontSize: 13,
    fontWeight: "600",
  },
  deleteButton: {
    position: "absolute",
    bottom: 12,
    right: 12,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
  },
  mediaUploadSmall: {
    width: 190,
    height: 160,
    backgroundColor: "#222",
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#2C2C2E",
    borderStyle: "dashed",
    overflow: "hidden",
  },
  mediaUploadContent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
  },
  mediaText: {
    color: "#fff",
    fontSize: 15,
    marginTop: 4,
    textAlign: "center",
  },
  mediaRequired: {
    color: "#FF453A",
    fontSize: 13,
    marginTop: 4,
  },
  mediaRequiredSmall: {
    marginTop: 8,
  },
  mediaUpload: {
    height: 160,
    backgroundColor: "#222",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#2C2C2E",
    borderStyle: "dashed",
  },
  input: {
    backgroundColor: "#222",
    borderRadius: 12,
    padding: 16,
    color: "#fff",
    fontSize: 17,
    marginBottom: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  inputLabel: {
    color: "#8E8E93",
    fontSize: 17,
  },
  inputValue: {
    color: "#fff",
  },
  textArea: {
    height: 120,
    textAlignVertical: "top",
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  quantityLabel: {
    color: "#fff",
    fontSize: 17,
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#222",
    borderRadius: 20,
    padding: 4,
  },
  quantityButton: {
    color: "#fff",
    fontSize: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  quantityValue: {
    color: "#fff",
    fontSize: 17,
    paddingHorizontal: 16,
  },
  variantsButton: {
    backgroundColor: "#1C1C1E",
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  variantsContent: {
    flex: 1,
  },
  variantsButtonText: {
    color: "#fff",
    fontSize: 17,
  },
  variantsDescription: {
    color: "#8E8E93",
    fontSize: 13,
  },
  variantsList: {
    marginTop: 8,
  },
  variantItem: {
    marginTop: 8,
  },
  variantText: {
    color: "#fff",
    fontSize: 15,
  },
  variantSeparator: {
    height: 1,
    backgroundColor: "#2C2C2E",
    marginTop: 8,
  },
  pricingOptions: {
    flexDirection: "row",
    marginBottom: 16,
    backgroundColor: "#2C2C2E",
    padding: 2,
    borderRadius: 100,
  },
  pricingOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 100,
  },
  pricingOptionSelected: {
    backgroundColor: "#fff",
  },
  pricingOptionText: {
    fontSize: 17,
    fontWeight: "500",
    color: "#8E8E93",
  },
  pricingOptionTextSelected: {
    color: "#000",
    fontWeight: "600",
  },
  toggleOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  toggleLabel: {
    color: "#fff",
    fontSize: 17,
    marginBottom: 4,
  },
  toggleDescription: {
    color: "#8E8E93",
    fontSize: 13,
    marginBottom: 24,
  },
  newBadge: {
    backgroundColor: "#1a96d2",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: "flex-start",
  },
  newBadgeText: {
    color: "#000",
    fontSize: 12,
    fontWeight: "600",
  },
  footer: {
    flexDirection: "row",
    marginBottom: 16,
  },
  draftButton: {
    flex: 1,
    backgroundColor: "#2C2C2E",
    borderRadius: 20,
    padding: 16,
    alignItems: "center",
    marginRight: 8,
  },
  draftButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "600",
  },
  premierNote: {
    marginBottom: 32,
  },
  premierNoteText: {
    color: "#8E8E93",
    fontSize: 13,
    marginBottom: 4,
  },
  learnMoreText: {
    color: "#0A84FF",
    fontSize: 13,
  },
  form: {
    padding: 16,
  },
  footerPublishButton: {
    flex: 1,
    backgroundColor: "#1a96d2",
    borderRadius: 20,
    padding: 16,
    alignItems: "center",
    marginLeft: 8,
  },
  footerPublishButtonDisabled: {
    opacity: 0.5,
  },
  footerPublishButtonText: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "600",
  },
  mediaCount: {
    color: "#8E8E93",
    fontSize: 13,
  },
  quantityInput: {
    color: "#fff",
    fontSize: 17,
    paddingHorizontal: 16,
    minWidth: 50,
    textAlign: "center",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  modalContent: {
    backgroundColor: "#2C2C2E",
    borderRadius: 14,
    padding: 20,
    width: "100%",
    alignItems: "center",
  },
  modalTitle: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 12,
    textAlign: "center",
  },
  modalText: {
    color: "#8E8E93",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 22,
  },
  modalButton: {
    backgroundColor: "#1a96d2",
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 100,
    width: "100%",
  },
  modalButtonText: {
    color: "#000",
    fontSize: 17,
    fontWeight: "600",
    textAlign: "center",
  },
  auctionInfoBox: {
    backgroundColor: "#2C2C2E",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  auctionInfoText: {
    color: "#8E8E93",
    fontSize: 15,
    textAlign: "center",
  },
  priceInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  currencySymbol: {
    color: "#8E8E93",
    fontSize: 17,
    marginRight: 4,
  },
  priceInput: {
    flex: 1,
    color: "#8E8E93",
    fontSize: 17,
    padding: 0,
  },
});
