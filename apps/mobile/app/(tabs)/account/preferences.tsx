import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  FlatList,
  Alert,
  Linking,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import React, { useState } from "react";
import { useUser } from "../../../hooks/useUser";
import { useMutation, useAction } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { GooglePlacesAutocomplete } from "react-native-google-places-autocomplete";
import "react-native-get-random-values";
import { CustomBottomSheet } from "../../../components/ui/BottomSheet";

export default function PreferencesScreen() {
  const router = useRouter();
  const { user, isLoading } = useUser();
  const updateUser = useMutation(api.users.update);
  const createConnectAccountLink = useAction(
    api.integration.stripe.createConnectAccountLink,
  );
  const [modalVisible, setModalVisible] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [form, setForm] = useState({
    street: "",
    city: "",
    state: "",
    country: "",
    zipCode: "",
    isDefault: false,
  });
  const [placeSelected, setPlaceSelected] = useState(false);

  const addresses = user?.preferences?.shippingAddresses || [];

  const openAddModal = () => {
    setEditingIndex(null);
    setForm({
      street: "",
      city: "",
      state: "",
      country: "",
      zipCode: "",
      isDefault: false,
    });
    setPlaceSelected(false);
    setModalVisible(true);
  };

  const openEditModal = (index: number) => {
    setEditingIndex(index);
    setForm(addresses[index]);
    setPlaceSelected(true);
    setModalVisible(true);
  };

  const handleSave = async () => {
    let newAddresses = [...addresses];
    if (form.isDefault) {
      newAddresses = newAddresses.map((addr: any) => ({
        ...addr,
        isDefault: false,
      }));
    }
    if (editingIndex === null) {
      newAddresses.push(form);
    } else {
      newAddresses[editingIndex] = form;
    }
    await updateUser({
      preferences: { 
        ...user.preferences, 
        shippingAddresses: newAddresses,
      },
      id: user._id,
    });
    setModalVisible(false);
  };

  const handleDelete = async (index: number) => {
    Alert.alert(
      "Delete Address",
      "Are you sure you want to delete this address?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            const newAddresses = addresses.filter(
              (_: any, i: number) => i !== index,
            );
            await updateUser({
              preferences: {
                ...user.preferences,
                shippingAddresses: newAddresses,
              },
              id: user._id,
            });
          },
        },
      ],
    );
  };

  const handleSetDefault = async (index: number) => {
    const newAddresses = addresses.map((addr: any, i: number) => ({
      ...addr,
      isDefault: i === index,
    }));
    await updateUser({
      preferences: {
        ...user.preferences,
        shippingAddresses: newAddresses,
      },
      id: user._id,
    });
  };

  const parseAddress = (details: any) => {
    const getComponent = (type: string) =>
      details.address_components?.find((c: any) => c.types.includes(type))
        ?.long_name || "";
    return {
      street:
        `${getComponent("street_number")} ${getComponent("route")}`.trim(),
      city:
        getComponent("locality") ||
        getComponent("sublocality") ||
        getComponent("administrative_area_level_2"),
      state: getComponent("administrative_area_level_1"),
      country: getComponent("country"),
      zipCode: getComponent("postal_code"),
    };
  };

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <View style={styles.scrollView}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={28} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.title}>Preferences</Text>
        </View>

        {/* Stripe Connect Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payouts</Text>
          {user?.stripeAccountId ? (
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginTop: 8,
                gap: 6,
              }}
            >
              <Ionicons name="checkmark-circle" size={20} color="#4cd964" />
              <Text style={{ color: "#4cd964", fontWeight: "bold" }}>
                Stripe Connected
              </Text>
            </View>
          ) : (
            <TouchableOpacity
              style={{
                backgroundColor: "#635bff",
                borderRadius: 8,
                padding: 12,
                margin: 16,
                alignItems: "center",
              }}
              onPress={async () => {
                try {
                  const res = await createConnectAccountLink({});
                  if (res?.url) {
                    Linking.openURL(res.url);
                  } else {
                    Alert.alert(
                      "Error",
                      "Could not get Stripe onboarding link.",
                    );
                  }
                } catch (e) {
                  Alert.alert("Error", "Could not connect to Stripe.");
                }
              }}
            >
              <Ionicons name="card-outline" size={20} color="#fff" />
              <Text
                style={{
                  color: "#fff",
                  fontWeight: "bold",
                  fontSize: 16,
                  marginTop: 2,
                }}
              >
                Connect Stripe
              </Text>
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Shipping Addresses</Text>
          <FlatList
            data={addresses}
            keyExtractor={(_: any, i: number) => i.toString()}
            renderItem={({ item, index }) => (
              <View
                style={{
                  backgroundColor: "#232325",
                  borderRadius: 8,
                  margin: 8,
                  padding: 12,
                }}
              >
                <Text
                  style={{
                    color: "#fff",
                    fontWeight: item.isDefault ? "bold" : "normal",
                  }}
                >
                  {item.street}, {item.city}, {item.state}, {item.country},{" "}
                  {item.zipCode}
                </Text>
                {item.isDefault && (
                  <Text style={{ color: "#4cd964", fontSize: 12 }}>
                    Default
                  </Text>
                )}
                <View style={{ flexDirection: "row", marginTop: 8 }}>
                  {!item.isDefault && (
                    <TouchableOpacity
                      onPress={() => handleSetDefault(index)}
                      style={{ marginRight: 16 }}
                    >
                      <Text style={{ color: "#4cd964" }}>Set Default</Text>
                    </TouchableOpacity>
                  )}
                  <TouchableOpacity
                    onPress={() => openEditModal(index)}
                    style={{ marginRight: 16 }}
                  >
                    <Text style={{ color: "#fff" }}>Edit</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => handleDelete(index)}>
                    <Text style={{ color: "#ff3b30" }}>Delete</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
            ListEmptyComponent={
              <Text style={{ color: "#aaa", margin: 16 }}>
                No shipping addresses added yet.
              </Text>
            }
          />
          <TouchableOpacity
            onPress={openAddModal}
            style={{
              backgroundColor: "#4cd964",
              borderRadius: 8,
              padding: 12,
              margin: 16,
              alignItems: "center",
            }}
          >
            <Text style={{ color: "#fff", fontWeight: "bold" }}>
              Add Address
            </Text>
          </TouchableOpacity>
        </View>
        <CustomBottomSheet
          isOpen={modalVisible}
          onClose={() => setModalVisible(false)}
          snapPoints={["90%"]}
          enableDynamicHeight={false}
        >
          <View style={styles.sheetContainer}>
            <View style={styles.sheetHeader}>
              <Text style={styles.sheetTitle}>
                {editingIndex === null ? "Add Address" : "Edit Address"}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Text style={styles.sheetCloseButton}>Cancel</Text>
              </TouchableOpacity>
            </View>
            {editingIndex === null && (
              <GooglePlacesAutocomplete
                placeholder="Search for address"
                onPress={(data, details = null) => {
                  if (details) {
                    const parsed = parseAddress(details);
                    setForm((f) => ({ ...f, ...parsed }));
                    setPlaceSelected(true);
                  }
                }}
                fetchDetails={true}
                query={{
                  key: "AIzaSyC7PLsSVuI8klCIVUoQpwrqeq7gEySwbUk",
                  language: "en",
                  types: "address",
                }}
                styles={{
                  textInput: {
                    backgroundColor: "#18181a",
                    color: "#fff",
                    borderBottomColor: "#333",
                    borderBottomWidth: 1,
                  },
                  container: { flex: 0, marginBottom: 10 },
                }}
                enablePoweredByContainer={false}
              />
            )}
            {(placeSelected || editingIndex !== null) && (
              <>
                <TextInput
                  placeholder="Street"
                  placeholderTextColor="#888"
                  style={inputStyle}
                  value={form.street}
                  onChangeText={(t) => setForm((f) => ({ ...f, street: t }))}
                />
                <TextInput
                  placeholder="City"
                  placeholderTextColor="#888"
                  style={inputStyle}
                  value={form.city}
                  onChangeText={(t) => setForm((f) => ({ ...f, city: t }))}
                />
                <TextInput
                  placeholder="State"
                  placeholderTextColor="#888"
                  style={inputStyle}
                  value={form.state}
                  onChangeText={(t) => setForm((f) => ({ ...f, state: t }))}
                />
                <TextInput
                  placeholder="Country"
                  placeholderTextColor="#888"
                  style={inputStyle}
                  value={form.country}
                  onChangeText={(t) => setForm((f) => ({ ...f, country: t }))}
                />
                <TextInput
                  placeholder="Zip Code"
                  placeholderTextColor="#888"
                  style={inputStyle}
                  value={form.zipCode}
                  onChangeText={(t) => setForm((f) => ({ ...f, zipCode: t }))}
                />
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    marginVertical: 8,
                  }}
                >
                  <TouchableOpacity
                    onPress={() =>
                      setForm((f) => ({ ...f, isDefault: !f.isDefault }))
                    }
                    style={{ marginRight: 8 }}
                  >
                    <Ionicons
                      name={form.isDefault ? "checkbox" : "square-outline"}
                      size={24}
                      color="#4cd964"
                    />
                  </TouchableOpacity>
                  <Text style={{ color: "#fff" }}>Set as default</Text>
                </View>
                <View style={styles.sheetBottomButton}>
                  <TouchableOpacity
                    style={styles.sheetSaveButton}
                    onPress={handleSave}
                    disabled={
                      !(
                        form.street &&
                        form.city &&
                        form.state &&
                        form.country &&
                        form.zipCode
                      )
                    }
                  >
                    <Text style={styles.sheetSaveText}>
                      {editingIndex === null ? "Add" : "Save"}
                    </Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </CustomBottomSheet>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 24,
  },
  backButton: {
    marginRight: 8,
  },
  title: {
    fontSize: 34,
    fontWeight: "bold",
    color: "#fff",
  },
  section: {
    marginTop: 32,
  },
  sectionTitle: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "bold",
    marginHorizontal: 16,
    marginBottom: 8,
    opacity: 0.9,
  },
  sheetContainer: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  sheetHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  sheetTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#fff",
    marginRight: 16,
  },
  sheetCloseButton: {
    color: "#888",
    fontSize: 16,
    fontWeight: "bold",
  },
  sheetBottomButton: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#333",
  },
  sheetSaveButton: {
    backgroundColor: "#4cd964",
    borderRadius: 6,
    padding: 16,
    alignItems: "center",
  },
  sheetSaveText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
});

const inputStyle = {
  backgroundColor: "#18181a",
  color: "#fff",
  borderRadius: 6,
  padding: 10,
  marginBottom: 10,
  borderWidth: 1,
  borderColor: "#333",
};
