"use client";

import React, { useState, useRef, useEffect, useCallback, useMemo } from "react";
import { TableToolbar } from "./_components/table-toolbar";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Button } from "@workspace/ui/components/button";
import { Loader2 } from "lucide-react";
import { User } from "@workspace/backend/convex/lib/types";
import UserList from "./list";
import { columns } from "./list/_components/columns";
import {
  ColumnDef,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";

type SearchField = "username" | "name" | "email" | "_id";

const UsersContent = () => {
  const [searchField, setSearchField] = useState<SearchField>("name");
  const [searchQuery, setSearchQuery] = useState("");
  const [cursor, setCursor] = useState<string | null>(null);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [showNewSellerApplicants, setShowNewSellerApplicants] = useState(false);
  const bottomRef = useRef<HTMLDivElement>(null);
  const isMounted = useRef(true);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [rowSelection, setRowSelection] = useState({});

  const aggregatedUsers = useQuery(api.users.aggregatedUsers);

  const paginationOptions = useMemo(() => {
    const filters = [];
    
    if (showNewSellerApplicants) {
      filters.push({ id: "role", value: "user", operator: "eq" });
      filters.push({ id: "interestedSellerStatus", value: "new", operator: "eq" });
    }

    return {
      paginationOpts: {
        numItems: showNewSellerApplicants ? 100 : 25,
        cursor: cursor
      },
      searchQuery,
      searchField,
      filters: filters.length > 0 ? filters : undefined,
    };
  }, [cursor, searchQuery, searchField, showNewSellerApplicants]);

  const usersQueryResult = useQuery(
    api.users.ADMIN_getUsers,
    paginationOptions
  );

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    let active = true;

    if (active) {
      setCursor(null);
      setAllUsers([]);
      setHasMore(true);
      setIsFetching(true);
    }
    
    return () => {
      active = false;
    };
  }, [searchQuery, searchField, showNewSellerApplicants]);

  useEffect(() => {
    let active = true;

    if (usersQueryResult && active) {
      if (isMounted.current) {
        setIsFetching(false);
        if (usersQueryResult.users && usersQueryResult.users.length > 0) {
          if (cursor === null) {
            setAllUsers(usersQueryResult.users);
          } else {
            setAllUsers(prev => {
              const existingIds = new Set(prev.map(u => u._id));
              const newUsers = usersQueryResult.users.filter(u => !existingIds.has(u._id));
              return [...prev, ...newUsers];
            });
          }
        } else if (cursor === null) {
          setAllUsers([]);
        }
        setHasMore(usersQueryResult.isDone === true ? false : true);
        setIsLoadingMore(false);
      }
    }

    return () => {
      active = false;
    };

  }, [usersQueryResult, cursor]);

  const loadMore = useCallback(() => {
    if (hasMore && !isLoadingMore && usersQueryResult?.continueCursor) {
      setIsLoadingMore(true);
      setCursor(usersQueryResult.continueCursor);
    }
  }, [hasMore, isLoadingMore, usersQueryResult?.continueCursor]);

  useEffect(() => {
    if (!hasMore || isLoadingMore) return;
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );
    const currentBottomRef = bottomRef.current;
    if (currentBottomRef) {
      observer.observe(currentBottomRef);
    }
    return () => {
      if (currentBottomRef) {
        observer.unobserve(currentBottomRef);
      }
    };
  }, [hasMore, isLoadingMore, loadMore]);

  const safeSetRowSelection = useCallback((updater: React.SetStateAction<typeof rowSelection>) => {
    if (isMounted.current) setRowSelection(updater);
  }, []);

  const safeSetSorting = useCallback((updater: React.SetStateAction<typeof sorting>) => {
    if (isMounted.current) setSorting(updater);
  }, []);

  const tableData = useMemo(() => allUsers, [allUsers]);
  const table = useReactTable({
    data: tableData as unknown as User[],
    columns: columns as ColumnDef<User>[],
    state: {
      sorting,
      rowSelection,
    },
    enableRowSelection: true,
    enableSorting: true,
    enableMultiSort: false,
    onRowSelectionChange: safeSetRowSelection,
    onSortingChange: safeSetSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  const handleToolbarSearch = useCallback(
    ({ field, value }: { field: SearchField; value: string }) => {
      setSearchField(field);
      setSearchQuery(value);
    },
    []
  );

  return (
    <>
      <div className="border-b border-muted p-3 w-full flex justify-between items-center">
        <TableToolbar 
          table={table} 
          setSearch={handleToolbarSearch} 
          showNewSellerApplicants={showNewSellerApplicants} 
          setShowNewSellerApplicants={setShowNewSellerApplicants} 
          aggregatedUsers={aggregatedUsers || 0}
        />
      </div>
      <div>
        <UserList
          table={table}
          isLoading={isFetching}
          columns={columns as ColumnDef<User>[]}
          hasMore={hasMore}
          isLoadingMore={isLoadingMore}
          onLoadMore={loadMore}
          bottomRef={bottomRef as React.RefObject<HTMLDivElement>}
          isMounted={isMounted}
        />
        {hasMore && allUsers.length > 0 && (
          <div ref={bottomRef} className="py-4 flex justify-center">
            {isLoadingMore ? (
              <Button variant="outline" disabled>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading more users...
              </Button>
            ) : (
              <Button variant="outline" onClick={loadMore}>
                Load more users
              </Button>
            )}
          </div>
        )}
        {!hasMore && allUsers.length > 0 && (
          <div className="py-4 text-center text-muted-foreground">
            All users loaded
          </div>
        )}
        {!isFetching && allUsers.length === 0 && (
          <div className="py-24 text-center text-muted-foreground">
            No users found
          </div>
        )}
      </div>
    </>
  );
};

export default UsersContent;
