"use client";

import * as React from "react";
import { format } from "date-fns";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@workspace/ui/components/dialog";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Calendar } from "@workspace/ui/components/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@workspace/ui/components/command";
import { User } from "@workspace/backend/convex/lib/types";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Textarea } from "@workspace/ui/components/textarea";
import { cn } from "@workspace/ui/lib/utils";
import { TASK_STATUS, TASK_PRIORITY, TaskStatus } from "@/lib/constants";
import {
  Icon,
  IconArrowUpRight,
  IconAt,
  IconCalendar,
  IconCircle,
  IconCircleDotted,
  IconSquareRoundedCheck,
} from "@tabler/icons-react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { CloseButton } from "../close-button";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { UserAvatar } from "@workspace/ui/components/user-avatar";
import { DatetimePicker } from "../date-time-picker";
import { FormField, FormItem } from "@workspace/ui/components/form";

interface TaskToEdit {
  _id: Id<"tasks">;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: "no_priority" | "urgent" | "high" | "medium" | "low";
  dueDate?: number;
  assignee?: { _id: Id<"users">; name: string } | null;
  related?: {
    _id: string;
    name: string | undefined;
    recordType: string | undefined;
  } | null;
}

interface CreateTaskModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  taskToEdit?: TaskToEdit;
  defaultStatus?: TaskStatus;
}

const taskSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  dueDate: z.date().optional(),
  status: z
    .enum(["backlog", "todo", "in_progress", "review", "done"])
    .default("todo"),
  priority: z
    .enum(["no_priority", "urgent", "high", "medium", "low"])
    .default("no_priority"),
  assignee: z
    .object({
      _id: z.custom<Id<"users">>(),
      name: z.string(),
    })
    .nullable(),
  related: z
    .object({
      _id: z.string(),
      name: z.string().optional(),
      recordType: z.string().optional(),
    })
    .optional()
    .nullable(),
});

type TaskFormData = z.infer<typeof taskSchema>;

const defaultFormValues: TaskFormData = {
  title: "",
  description: "",
  status: "todo",
  priority: "no_priority",
  assignee: null,
  related: null,
};

export function CreateTaskModal({
  open,
  onOpenChange,
  taskToEdit,
  defaultStatus,
}: CreateTaskModalProps) {
  const create = useMutation(api.tasks.create);
  const update = useMutation(api.tasks.update);
  const user = useQuery(api.users.viewer);

  const [assigneePopoverOpen, setAssigneePopoverOpen] = React.useState(false);
  const [statusOpen, setStatusOpen] = React.useState(false);
  const [priorityOpen, setPriorityOpen] = React.useState(false);
  const [calendarOpen, setCalendarOpen] = React.useState(false);
  const [searchTermUsers, setSearchTermUsers] = React.useState("");

  const filteredUsers = React.useMemo(() => (user ? [user] : []), [user]);

  const defaultAssignee = user
    ? {
        _id: user._id as Id<"users">,
        name: user.name || `${user.firstName} ${user.lastName}`.trim(),
      }
    : null;

  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      ...defaultFormValues,
      assignee: defaultAssignee,
    },
  });

  React.useEffect(() => {
    if (!open) {
      form.reset({
        ...defaultFormValues,
        assignee: defaultAssignee,
      });
      setSearchTermUsers("");
      return;
    }

    if (taskToEdit) {
      form.reset({
        title: taskToEdit.title,
        description: taskToEdit.description || "",
        status: taskToEdit.status,
        priority: taskToEdit.priority,
        dueDate: taskToEdit.dueDate ? new Date(taskToEdit.dueDate) : undefined,
        assignee: taskToEdit.assignee,
        related: taskToEdit.related || null,
      });
    } else {
      form.reset({
        ...defaultFormValues,
        status: defaultStatus || defaultFormValues.status,
        assignee: defaultAssignee,
      });
    }
  }, [open, taskToEdit, form, defaultStatus, user]);

  const onSubmit = async (formData: TaskFormData) => {
    try {
      if (taskToEdit) {
        const result = await update({
          id: taskToEdit._id,
          title: formData.title,
          description: formData.description || undefined,
          dueDate: formData.dueDate?.getTime(),
          related: formData.related?._id
            ? [formData.related._id as Id<"contacts">]
            : null,
          assignee: formData.assignee?._id,
          status: formData.status,
          priority: formData.priority,
        });

        if (result?.success) {
          onOpenChange(false);
        }
      } else {
        const result = await create({
          title: formData.title,
          description: formData.description || undefined,
          dueDate: formData.dueDate?.getTime(),
          related: formData.related?._id
            ? [formData.related._id as Id<"contacts">]
            : null,
          assignee: formData.assignee?._id,
          status: formData.status,
          priority: formData.priority,
        });

        if (result) {
          onOpenChange(false);
        }
      }
    } catch (error) {
      console.error("Failed to save task:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        close={false}
        className="sm:max-w-3xl p-0 m-0"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogTitle className="sr-only">
          {taskToEdit ? "Edit Task" : "Create Task"}
        </DialogTitle>
        <DialogDescription className="sr-only">
          {taskToEdit ? "Edit an existing task" : "Create a new task"}
        </DialogDescription>

        <div className="flex items-center justify-between p-2 px-4">
          <div className="flex flex-row items-center gap-x-2">
            <IconSquareRoundedCheck className="h-5 w-5" />
            <span className="text-md font-light">
              {taskToEdit ? "Edit Task" : "Create Task"}
            </span>
          </div>
          <CloseButton onClick={() => onOpenChange(false)} />
        </div>

        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col w-full border-t border-input -mt-4"
        >
          <div className="flex flex-col p-2 dark:bg-input/30">
            <Input
              {...form.register("title")}
              autoFocus
              className="shadow-none selection:bg-transparent dark:bg-transparent bg-transparent file:bg-transparent ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 w-full font-semibold border-none rounded-none text-xl md:text-xl"
              placeholder="Title"
            />
            {form.formState.errors.title && (
              <span className="text-red-500 text-sm">
                {form.formState.errors.title.message}
              </span>
            )}
            <Textarea
              {...form.register("description")}
              className="shadow-none ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 w-full border-none rounded-none p-3 bg-transparent dark:bg-transparent text-muted-foreground text-md md:text-md"
              placeholder="Add a description..."
              style={{ resize: "none" }}
            />
          </div>

          <div className="flex flex-row items-center p-2 justify-between border-t border-input">
            <div className="flex flex-row items-center gap-x-2">
              {/* Status */}
              <Popover open={statusOpen} onOpenChange={setStatusOpen}>
                <PopoverTrigger asChild>
                  <Button type="button" variant="small" size="sm">
                    <div className="flex items-center gap-2">
                      {TASK_STATUS.find(
                        (st) => st.value === form.watch("status"),
                      )?.icon && (
                        <div
                          className={cn(
                            "h-4 w-4",
                            TASK_STATUS.find(
                              (st) => st.value === form.watch("status"),
                            )?.color,
                          )}
                        >
                          {React.createElement(
                            TASK_STATUS.find(
                              (st) => st.value === form.watch("status"),
                            )!.icon,
                            {
                              className: cn(
                                "h-4 w-4",
                                TASK_STATUS.find(
                                  (st) => st.value === form.watch("status"),
                                )?.color,
                              ),
                            },
                          )}
                        </div>
                      )}
                      <span>
                        {TASK_STATUS.find(
                          (st) => st.value === form.watch("status"),
                        )?.label || "Status"}
                      </span>
                    </div>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-fit p-0">
                  <Command>
                    <CommandInput placeholder="Search status..." />
                    <CommandList>
                      <CommandEmpty>No status found.</CommandEmpty>
                      <CommandGroup>
                        {TASK_STATUS.map((st) => (
                          <CommandItem
                            key={st.value}
                            value={st.value}
                            onSelect={() => {
                              form.setValue(
                                "status",
                                st.value as TaskFormData["status"],
                              );
                              setStatusOpen(false);
                            }}
                          >
                            {React.createElement(st.icon, {
                              className: cn("h-4 w-4 mr-2", st.color),
                            })}
                            {st.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>

              {/* Priority */}
              <Popover open={priorityOpen} onOpenChange={setPriorityOpen}>
                <PopoverTrigger asChild>
                  <Button type="button" variant="small" size="sm">
                    <div className="flex items-center gap-2">
                      {TASK_PRIORITY.find(
                        (pr) => pr.value === form.watch("priority"),
                      )?.icon &&
                        React.createElement(
                          TASK_PRIORITY.find(
                            (pr) => pr.value === form.watch("priority"),
                          )!.icon,
                          {
                            className: cn(
                              "h-4 w-4",
                              TASK_PRIORITY.find(
                                (pr) => pr.value === form.watch("priority"),
                              )?.color,
                            ),
                          },
                        )}
                      <span
                        className={cn(
                          form.watch("priority") === "no_priority" &&
                            "text-muted-foreground",
                        )}
                      >
                        {TASK_PRIORITY.find(
                          (pr) => pr.value === form.watch("priority"),
                        )?.label || "Priority"}
                      </span>
                    </div>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-fit p-0">
                  <Command>
                    <CommandInput placeholder="Search priority..." />
                    <CommandList>
                      <CommandEmpty>No priority found.</CommandEmpty>
                      <CommandGroup>
                        {TASK_PRIORITY.map((pr) => (
                          <CommandItem
                            key={pr.value}
                            value={pr.value}
                            onSelect={() => {
                              form.setValue(
                                "priority",
                                pr.value as TaskFormData["priority"],
                              );
                              setPriorityOpen(false);
                            }}
                          >
                            {React.createElement(pr.icon, {
                              className: cn("h-4 w-4 mr-2", pr.color),
                            })}
                            {pr.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>

              {/* Due Date */}
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button type="button" variant="small" size="sm">
                          {field.value ? (
                            <div className="flex items-center gap-2">
                              <IconCalendar className="h-4 w-4" />
                              {format(field.value, "MMM d, yyyy, h:mm a")}
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <IconCalendar className="h-4 w-4" />
                              Due date
                            </div>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-[600px] p-0 rounded-xl"
                        align="start"
                      >
                        <DatetimePicker
                          value={field.value}
                          onChange={(date) => {
                            field.onChange(date);
                          }}
                          onClose={() => {
                            const popoverElement = document.querySelector(
                              "[data-radix-popper-content-id]",
                            );
                            if (popoverElement) {
                              const closeButton = popoverElement.querySelector(
                                "[data-radix-popper-close-trigger]",
                              );
                              if (closeButton instanceof HTMLElement) {
                                closeButton.click();
                              }
                            }
                          }}
                        />
                      </PopoverContent>
                    </Popover>
                  </FormItem>
                )}
              />

              {/* Assignee */}
              <Popover
                open={assigneePopoverOpen}
                onOpenChange={setAssigneePopoverOpen}
              >
                <PopoverTrigger asChild>
                  <Button type="button" variant="small" size="sm">
                    {form.watch("assignee") ? (
                      <div
                        className={`flex items-center gap-2 ${form.watch("assignee")?._id === user?._id ? "text-muted-foreground" : ""}`}
                      >
                        <IconAt className="h-4 w-4" />
                        <span>
                          Assigned to{" "}
                          {form.watch("assignee")?._id === user?._id
                            ? "you"
                            : form.watch("assignee")?.name}
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <IconAt className="h-4 w-4" />
                        Unassigned
                      </div>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-fit p-0">
                  <Command>
                    <CommandInput
                      placeholder="Search users..."
                      value={searchTermUsers}
                      onValueChange={setSearchTermUsers}
                    />
                    <CommandList>
                      <CommandEmpty>No users found.</CommandEmpty>
                      <CommandGroup>
                        <CommandItem
                          value="unassign"
                          onSelect={() => {
                            form.setValue("assignee", null);
                            setAssigneePopoverOpen(false);
                          }}
                        >
                          <IconAt className="h-4 w-4 mr-2" />
                          Unassign
                        </CommandItem>
                        <CommandSeparator className="my-1" />
                        {filteredUsers?.map((u) => (
                          <CommandItem
                            key={u._id}
                            value={u._id}
                            onSelect={() => {
                              form.setValue("assignee", {
                                _id: u._id as Id<"users">,
                                name:
                                  u.name ||
                                  `${u.firstName} ${u.lastName}`.trim(),
                              });
                              setAssigneePopoverOpen(false);
                            }}
                          >
                            <UserAvatar user={u as User} size="sm" />
                            <span className="ml-2">
                              {u.name || `${u.firstName} ${u.lastName}`.trim()}
                            </span>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                size="sm"
                disabled={form.formState.isSubmitting}
              >
                {taskToEdit ? "Update Task" : "Create Task"}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
