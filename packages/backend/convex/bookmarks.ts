import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getCurrentUser, isValidStorageId } from "./helpers/utils";

/**
 * Toggle a bookmark for a stream
 */
export const toggleBookmark = mutation({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("User not found");
    }

    // Check if bookmark exists
    const existingBookmark = await ctx.db
      .query("bookmarks")
      .withIndex("by_user_stream", (q) =>
        q.eq("userId", user._id).eq("streamId", args.streamId),
      )
      .first();

    if (existingBookmark) {
      // Remove bookmark if it exists
      await ctx.db.delete(existingBookmark._id);
      return { bookmarked: false };
    } else {
      // Add bookmark if it doesn't exist
      await ctx.db.insert("bookmarks", {
        userId: user._id,
        streamId: args.streamId,
      });
      return { bookmarked: true };
    }
  },
});

/**
 * Check if a stream is bookmarked by the current user
 */
export const isStreamBookmarked = query({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("User not found");
    }

    const bookmark = await ctx.db
      .query("bookmarks")
      .withIndex("by_user_stream", (q) =>
        q.eq("userId", user._id).eq("streamId", args.streamId),
      )
      .first();

    return !!bookmark;
  },
});

/**
 * Get all bookmarked streams for the current user
 */
export const getBookmarkedStreams = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) return [];

    // Get all user's bookmarks
    const bookmarks = await ctx.db
      .query("bookmarks")
      .filter((q) => q.eq(q.field("userId"), user._id))
      .collect();

    // Get the full stream data for each bookmark
    const streams = await Promise.all(
      bookmarks.map(async (bookmark) => {
        const stream = await ctx.db.get(bookmark.streamId);
        if (!stream) return null;

        // Get the streamer data
        const streamer = await ctx.db.get(stream.userId);
        if (!streamer) return null;

        // Get proper URLs for any image IDs
        let profileImage = null;
        if (streamer.image) {
          if (isValidStorageId(streamer.image)) {
            profileImage = await ctx.storage.getUrl(streamer.image);
          } else if (
            typeof streamer.image === "string" &&
            streamer.image.startsWith("http")
          ) {
            profileImage = streamer.image;
          }
        }

        let thumbnailUrl = null;
        if (stream.thumbnail) {
          if (isValidStorageId(stream.thumbnail)) {
            thumbnailUrl = await ctx.storage.getUrl(stream.thumbnail);
          } else if (stream.thumbnail.startsWith("http")) {
            thumbnailUrl = stream.thumbnail;
          }
        }

        // Return the combined data
        return {
          ...stream,
          thumbnail: thumbnailUrl,
          viewerCount: 0, // Default value
          streamer: {
            username: streamer.username,
            profileImage,
            color: streamer.color || "#2C2C2E",
          },
        };
      }),
    );

    // Filter out null streams (those that couldn't be found)
    return streams.filter(Boolean);
  },
});
