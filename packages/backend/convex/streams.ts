import { v } from "convex/values";
import { action, mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Doc, Id } from "./_generated/dataModel";
import { categories, Category } from "@workspace/lib/constants/categories";
import { paginationOptsValidator } from "convex/server";
import { getCurrentUser, isValidStorageId } from "./helpers/utils";
import { api } from "./_generated/api";

export type StreamCategory = typeof categories[number];

export type StreamWithDetails = Doc<"streams"> & {
  streamer?: Doc<"users">;
  hostName: string;
  productName?: string | null;
};

export const sendMessage = mutation({
  args: {
    streamId: v.id("streams"),
    text: v.string(),
    type: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be logged in to send a message.");
    }
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found.");
    }

    const stream = await ctx.db.get(args.streamId);

    if (stream?.blockedUserIds?.includes(userId)) {
        throw new Error("You are blocked from sending messages in this stream.");
    }

    await ctx.db.insert("streamMessages", {
      streamId: args.streamId,
      userId: userId,
      userName: user.name ?? user.email ?? "Anonymous",
      text: args.text,
      type: args.type ?? "user",
    });

    const text = args.text ?? "";
    // Extract usernames from mentions in the form @username
    const mentionRegex = /@([a-zA-Z0-9_]+)/g;
    const mentionedUsernames = Array.from(text.matchAll(mentionRegex)).map(match => match[1]?.toLowerCase());
    if (mentionedUsernames.length > 0) {
      const mentionedUsers = await ctx.db
        .query("users")
        .filter(q => q.or(...mentionedUsernames.map(username => q.eq(q.field("username"), username))))
        .collect();
      const streamHost = stream ? await ctx.db.get(stream.hostId) : null;
      const taggerUsername = user.username;
      const streamHostUsername = streamHost?.username || "the host";
      const streamUrl = `/stream/${args.streamId}`;
      for (const mentionedUser of mentionedUsers) {
        if (!mentionedUser || !mentionedUser._id) continue;
        if (mentionedUser._id === userId) continue;
        if (!mentionedUsernames.includes(mentionedUser.username)) continue;
        
        await ctx.db.insert("notifications", {
          userId: mentionedUser._id,
          type: "mention",
          actorId: userId,
          read: false,
          data: {
            taggerId: userId,
            taggerUsername,
            streamId: args.streamId,
            streamHostUsername,
            streamUrl,
            originalMessage: args.text,
          },
        });
      }
    }
    return null;
  },
});

export const listMessages = query({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args) => {
    const messages = await ctx.db
      .query("streamMessages")
      .withIndex("by_streamId", (q) => q.eq("streamId", args.streamId))
      .order("asc")
      .take(100);

    const userIds = [...new Set(messages.map(m => m.userId))];
    
    const users = await Promise.all(
      userIds.map(async (userId) => {
        const user = await ctx.db.get(userId);
        return {
          _id: userId,
          username: user?.username ?? "Anonymous",
          image: user?.image,
          color: user?.color,
        };
      })
    );

    return messages.map(message => ({
      ...message,
      user: users.find(u => u._id === message.userId)
    }));
  },
});

export const createStream = action({
  args: {
    title: v.string(),
    description: v.optional(v.string()),  
    category: v.string(), 
    subcategory: v.optional(v.string()),
    format: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    explicitContent: v.optional(v.boolean()),
    muteWords: v.optional(v.array(v.string())),
    visibility: v.optional(v.string()), 
    scheduledTime: v.optional(v.number()),
    thumbnail: v.optional(v.id("_storage")),
    eventType: v.optional(v.string()),
    moderatorIds: v.optional(v.array(v.id("users"))),
  },
  handler: async (ctx, args): Promise<Id<"streams">> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("User must be logged in to create a stream.");

    const tempRoomName = `stream-placeholder-${Date.now()}`;

    const streamId = await ctx.runMutation(api.streams.insertStream, {
      hostId: userId as Id<"users">,
      title: args.title,
      description: args.description,
      thumbnail: args.thumbnail,
      roomName: tempRoomName,
      category: args.category,
      subcategory: args.subcategory,
      format: args.format,
      tags: args.tags,
      explicitContent: args.explicitContent,
      muteWords: args.muteWords,
      visibility: args.visibility,
      eventType: args.eventType,
      status: "scheduled", 
      scheduledTime: args.scheduledTime,
      isActive: false,   
      moderatorIds: [],
      blockedUserIds: [],
    });

    await ctx.runMutation(api.streams.patchStream, {
      streamId,
      data: { roomName: `stream-${streamId}` },
    });

    let ingressInfo = null;
    try {
      ingressInfo = await ctx.runAction(api.integration.livekit.generateIngestInfo, {
        streamId,
      });

      await ctx.runMutation(api.streams.patchStream, {
        streamId,
        data: {
          ingestUrl: ingressInfo.ingestUrl,
          streamKey: ingressInfo.streamKey,
        }
      });
    } catch (e) {
      throw new Error("Failed to create LiveKit ingress");
    }

    return streamId;
  },
});

export const insertStream = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),  
    category: v.string(), 
    subcategory: v.optional(v.string()),
    format: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    explicitContent: v.optional(v.boolean()),
    muteWords: v.optional(v.array(v.string())),
    visibility: v.optional(v.string()),
    scheduledTime: v.optional(v.number()),
    thumbnail: v.optional(v.id("_storage")),
    eventType: v.optional(v.string()),
    roomName: v.string(),
    hostId: v.id("users"),
    status: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    moderatorIds: v.optional(v.array(v.id("users"))),
    blockedUserIds: v.optional(v.array(v.id("users"))),
  },
  handler: async (ctx, args) => {
    const streamId = await ctx.db.insert("streams", {
      hostId: args.hostId,
      title: args.title,
      description: args.description,
      thumbnail: args.thumbnail,
      roomName: args.roomName,
      category: args.category,
      subcategory: args.subcategory,
      format: args.format,
      tags: args.tags,
      explicitContent: args.explicitContent,
      muteWords: args.muteWords,
      visibility: args.visibility,
      eventType: args.eventType,
      status: "scheduled", 
      scheduledTime: args.scheduledTime,
      isActive: false,   
      moderatorIds: [],
      blockedUserIds: [],
    });
    
    return streamId;
  }
});

export const patchStream = mutation({
  args: {
    streamId: v.id("streams"),
    data: v.any(),
  },
  handler: async (ctx, { streamId, data }) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const stream = await ctx.db.get(streamId);
    if (!stream) throw new Error("Stream not found");

    if (stream.hostId !== user._id) throw new Error("Not authorized");
    await ctx.db.patch(streamId, data);
    return { success: true };
  },
});

export const goLive = mutation({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("User must be logged in.");

    const stream = await ctx.db.get(args.streamId);
    if (!stream) throw new Error("Stream not found.");
    if (stream.hostId !== userId) throw new Error("Only the host can start the stream.");
    if (stream.status !== "scheduled") throw new Error("Stream is not scheduled and cannot be started.");

    await ctx.db.patch(args.streamId, { status: "live", isActive: true });
    return null;
  },
});

export const endStream = mutation({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args): Promise<null> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be logged in.");
    }

    const stream = await ctx.db.get(args.streamId);
    if (!stream) {
      throw new Error("Stream not found.");
    }

    if (stream.hostId !== userId) {
      throw new Error("Only the host can end the stream.");
    }

    await ctx.db.patch(args.streamId, { status: "ended", isActive: false });
    return null;
  },
});

export const listActiveStreams = query({
  args: { category: v.optional(v.string()) },
  handler: async (ctx, args): Promise<StreamWithDetails[]> => {
    const currentUser = await getCurrentUser(ctx);
    let streams;
    if (args.category) {
        const liveStreams = await ctx.db.query("streams")
            .withIndex("by_category_and_status", q => q.eq("category", args.category!).eq("status", "live"))
            .order("desc")
            .collect();
        
        const scheduledStreams = await ctx.db.query("streams")
            .withIndex("by_category_and_status", q => q.eq("category", args.category!).eq("status", "scheduled"))
            .order("desc")
            .collect();
        
        streams = [...liveStreams, ...scheduledStreams];
    } else {
        const liveStreams = await ctx.db.query("streams")
            .withIndex("by_status", q => q.eq("status", "live"))
            .order("desc")
            .collect();
        
        const scheduledStreams = await ctx.db.query("streams")
            .withIndex("by_status", q => q.eq("status", "scheduled"))
            .order("desc")
            .collect();
        
        streams = [...liveStreams, ...scheduledStreams];
    }

    streams = streams.filter(stream => {
      if (stream.visibility !== "private") return true;
      if (!currentUser) return false;
      if (stream.hostId === currentUser._id) return true;
      if (Array.isArray(stream.moderatorIds) && stream.moderatorIds.includes(currentUser._id)) return true;
      return false;
    });

    const streamsWithDetails: StreamWithDetails[] = await Promise.all(
      streams.map(async (stream) => {
        const host = await ctx.db.get(stream.hostId);
        let product = null;
        if (stream.productId) {
          product = await ctx.db.get(stream.productId);
        }
        return {
          ...stream,
          streamer: host || undefined,
          hostName: host?.username ?? "Unknown Host",
          productName: product?.name ?? "No product",
        };
      })
    );
    return streamsWithDetails;
  },
});

export const getStream = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args): Promise<StreamWithDetails | null> => {
    const stream = await ctx.db.get(args.streamId);
    if (!stream) return null;

    const host = await ctx.db.get(stream.hostId);
    let product = null;
    if (stream.productId) {
      product = await ctx.db.get(stream.productId);
    }
    return {
      ...stream,
      streamer: host || undefined,
      hostName: host?.name ?? host?.email ?? "Unknown Host",
      productName: product?.name,
    };
  },
});

export const getStreamById = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.streamId);
  },
});

export const getMyScheduledStreams = query({
    args: {},
    handler: async (ctx) => {
        const userId = await getAuthUserId(ctx);
        if (!userId) return [];

        const streams = await ctx.db.query("streams")
            .withIndex("by_hostId", q => q.eq("hostId", userId))
            .filter(q => q.eq(q.field("status"), "scheduled"))
            .order("desc")
            .collect();
        
        return streams;
    }
});

export const listByUser = query({
  args: {
    paginationOpts: v.optional(paginationOptsValidator),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);
    if (!currentUser) throw new Error("Not authenticated");

    let streamQuery = ctx.db
      .query("streams")
      .filter((q) => q.eq(q.field("hostId"), currentUser._id))
      .order("desc");

    let streams;
    let isDone = true;
    let continueCursor = null;
    if (args.paginationOpts) {
      const paginated = await streamQuery.paginate(args.paginationOpts);
      streams = paginated.page;
      isDone = paginated.isDone;
      continueCursor = paginated.continueCursor;
    } else {
      streams = await streamQuery.collect();
    }

    const streamsWithUser = await Promise.all(
      streams.map(async (stream) => {
        let thumbnailUrl = null;
        if (stream.thumbnail && isValidStorageId(stream.thumbnail)) {
          thumbnailUrl = await ctx.storage.getUrl(stream.thumbnail);
        }
        return {
          ...stream,
          streamer: currentUser,
          thumbnailUrl,
        };
      }),
    );

    return args.paginationOpts
      ? { page: streamsWithUser, isDone, continueCursor }
      : streamsWithUser;
  },
});

export const listByCategory = query({
  args: {
    category: v.optional(v.string()),
    subcategory: v.optional(v.string()),
    paginationOpts: v.optional(paginationOptsValidator),
  },
  handler: async (ctx, args) => {
    let streamQuery = ctx.db.query("streams").order("desc");

    if (args.category) {
      streamQuery = streamQuery.filter((q) =>
        q.eq(q.field("category"), args.category),
      );
    }

    if (args.subcategory) {
      streamQuery = streamQuery.filter((q) =>
        q.eq(q.field("subcategory"), args.subcategory),
      );
    }

    const paginatedStreams = await streamQuery.paginate(
      args.paginationOpts || { numItems: 10, cursor: null },
    );

    const currentUser = await getCurrentUser(ctx);
    let userBookmarks = new Set();
    if (currentUser) {
      const bookmarks = await ctx.db
        .query("bookmarks")
        .withIndex("by_user", (q) => q.eq("userId", currentUser._id))
        .collect();
      userBookmarks = new Set(bookmarks.map((b) => b.streamId));
    }

    const streamsWithUserInfo = await Promise.all(
      paginatedStreams.page.map(async (stream) => {
        const user = await ctx.db.get(stream.hostId);
        if (!user) return null;

        let thumbnailUrl: string | null = null;
        if (stream.thumbnail) {
          if (isValidStorageId(stream.thumbnail)) {
            thumbnailUrl = await ctx.storage.getUrl(stream.thumbnail);
          } else if (stream.thumbnail.startsWith("http")) {
            thumbnailUrl = stream.thumbnail;
          }
        }

        let viewerCount = 0;

        return {
          ...stream,
          streamer: user,
          thumbnailUrl:
            thumbnailUrl ||
            user.image,
          isBookmarked: userBookmarks.has(stream._id),
          viewerCount,
        };
      }),
    );

    return {
      page: streamsWithUserInfo.filter(Boolean),
      isDone: paginatedStreams.isDone,
      continueCursor: paginatedStreams.continueCursor,
    };
  },
});

export const getStreams = query({
  args: {
    paginationOpts: v.optional(paginationOptsValidator),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    // Get user preferences
    const preferences = user.preferences || {};
    const followedCategories = preferences.categories || [];
    const followedSubcategories = preferences.subcategories || [];

    // Get followed users (hosts/sellers)
    const followingResult = await ctx.runQuery(api.users.getFollowing, {
      userId: user._id,
      paginationOpts: { numItems: 100, cursor: null },
    });
    const followedHostIds = (followingResult.users || []).map((u) => u._id);

    // Build filter conditions
    const orConditions: Array<(q: any) => any> = [];
    if (followedCategories.length > 0) {
      orConditions.push(...followedCategories.map((cat) => (q: any) => q.eq(q.field("category"), cat)));
    }
    if (followedSubcategories.length > 0) {
      orConditions.push(...followedSubcategories.map((subcat) => (q: any) => q.eq(q.field("subcategory"), subcat)));
    }
    if (followedHostIds.length > 0) {
      orConditions.push(...followedHostIds.map((hostId) => (q: any) => q.eq(q.field("hostId"), hostId)));
    }

    let streamQuery = ctx.db.query("streams").order("desc");
    if (orConditions.length > 0) {
      streamQuery = streamQuery.filter((q) => q.or(...orConditions.map((cond) => cond(q))));
    }

    streamQuery = streamQuery.filter((q) => 
      q.or(
        q.eq(q.field("visibility"), "public"),
        q.and(
          q.eq(q.field("visibility"), "private"),
          q.eq(q.field("hostId"), user._id)
        )
      )
    );

    const paginated = await streamQuery.paginate(
      args.paginationOpts || { numItems: 30, cursor: null }
    );

    // Enrich streams with streamer info
    const streamsWithUser = await Promise.all(
      paginated.page.map(async (stream) => {
        const streamer = await ctx.db.get(stream.hostId);
        return {
          ...stream,
          streamer: streamer || null,
        };
      })
    );

    return {
      page: streamsWithUser,
      isDone: paginated.isDone,
      continueCursor: paginated.continueCursor,
    };
  },
});

