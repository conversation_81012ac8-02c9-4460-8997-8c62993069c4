import { v } from "convex/values";
import { Id } from "../_generated/dataModel";
import {
  Permission,
  RoomWithMembers,
  UserRole,
  UserStatus,
} from "../lib/types";
import { PERMISSIONS, ROLES } from "../lib/constants";

/**
 * @name ROLE_PERMISSIONS
 * @description Define role-permission mappings
 */
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  ADMIN: [
    PERMISSIONS.ROOM.CREATE,
    PERMISSIONS.ROOM.DELETE,
    PERMISSIONS.ROOM.UPDATE,
    PERMISSIONS.ROOM.BLOCK_USER,
    PERMISSIONS.ROOM.UNBLOCK_USER,
    PERMISSIONS.ROOM.MUTE_USER,
    PERMISSIONS.ROOM.UNMUTE_USER,
    PERMISSIONS.ROOM.MUTE_ALL,
    PERMISSIONS.ROOM.UNMUTE_ALL,
    PERMISSIONS.ROOM.MUTE_NOTIFICATIONS,
    PERMISSIONS.ROOM.UNMUTE_NOTIFICATIONS,
    PERMISSIONS.USER.UPDATE,
    PERMISSIONS.USER.DELETE,
  ],
  MEMBER: [PERMISSIONS.ROOM.JOIN] as Permission[],
  GUEST: [PERMISSIONS.ROOM.JOIN] as Permission[],
  BLOCKED: [],
};

/**
 * @name hasPermission
 * @description Check if a user has a specific permission in a room
 */
export function hasPermission(
  room: RoomWithMembers,
  userId: Id<"users">,
  permission: Permission,
): boolean {
  const member = room.users.find((u) => u.id === userId);
  if (!member || member.status !== "online") return false;

  const rolePermissions = ROLE_PERMISSIONS[member.role];
  return rolePermissions.includes(permission);
}

/**
 * @name hasAnyPermission
 * @description Check if a user has any of the specified permissions in a room
 */
export function hasAnyPermission(
  room: RoomWithMembers,
  userId: Id<"users">,
  permissions: Permission[],
): boolean {
  return permissions.some((permission) =>
    hasPermission(room, userId, permission),
  );
}

/**
 * @name hasAllPermissions
 * @description Check if a user has all of the specified permissions in a room
 */
export function hasAllPermissions(
  room: RoomWithMembers,
  userId: Id<"users">,
  permissions: Permission[],
): boolean {
  return permissions.every((permission) =>
    hasPermission(room, userId, permission),
  );
}

/**
 * @name getUserPermissions
 * @description Get all permissions for a user in a room
 */
export function getUserPermissions(
  room: RoomWithMembers,
  userId: Id<"users">,
): Permission[] {
  const member = room.users.find((u) => u.id === userId);
  if (!member || member.status !== "online") return [];
  return ROLE_PERMISSIONS[member.role];
}

/**
 * @name permissionValidator
 * @description Validator for permission strings
 */
export const permissionValidator = v.union(
  ...Object.values(PERMISSIONS).flatMap((category) =>
    Object.values(category).map((permission) => v.literal(permission)),
  ),
);
